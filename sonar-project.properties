sonar.projectKey=paris2-api-risk-assessment
sonar.projectName=paris2-api-risk-assessment
sonar.sourceEncoding=UTF-8
sonar.sources=src/
sonar.tests=src/__tests__/
sonar.cpd.exclusions=src/db/**
sonar.exclusions=src/__tests__/**,**/*.d.ts,src/models/**,src/types/**,src/enums/**,src/validation/**,src/dtos/**
sonar.coverage.exclusions=src/__tests__/**,**/*.d.ts,src/models/**,src/types/**,src/enums/**,src/config/*,src/db/repository/__mocks__/*,src/swagger/*,src/validation/**,src/dtos/**,src/db/**
sonar.javascript.coveragePlugin=lcov
sonar.javascript.lcov.reportPaths=coverage/lcov.info,coverage-sync/lcov.info
sonar.host.url=https://sonar-dev.fleetship.com/
sonar.scm.provider=git
