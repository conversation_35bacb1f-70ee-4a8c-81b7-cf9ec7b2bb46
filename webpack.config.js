const slsw = require('serverless-webpack');
const path = require('path');
const nodeExternals = require('webpack-node-externals');
const babelConfig = require('./babel.config');

module.exports = {
  entry: slsw.lib.entries,
  target: 'node',
  mode: slsw.lib.webpack.isLocal ? 'development' : 'production',
  optimization: {
    // We no not want to minimize our code.
    minimize: false,
  },
  devtool: 'nosources-source-map',
  externals: [nodeExternals()],
  module: {
    rules: [
      {
        test: /\.[tj]s$/,
        exclude: [/(node_modules|bower_components)/, path.resolve(__dirname, './scripts/')],
        use: {
          loader: 'babel-loader',
          options: {
            presets: babelConfig.presets,
          },
        },
      },
    ],
  },
  output: {
    libraryTarget: 'commonjs2',
    path: path.join(__dirname, '.webpack', 'risk-assessment'),
    filename: '[name].js',
    sourceMapFilename: '[file].map',
  },
  resolve: {
    alias: {
      src: path.resolve(__dirname, './src'),
    },
    extensions: ['.ts', '.js', '.tsx'],
  },
  plugins: [],
};
