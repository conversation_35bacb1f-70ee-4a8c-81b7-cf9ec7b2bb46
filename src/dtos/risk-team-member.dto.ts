import Joi from 'joi';

const teamMemberSchema = Joi.object({
  seafarer_id: Joi.number().required(),
  seafarer_person_id: Joi.number().allow(null),
  seafarer_hkid: Joi.number().allow(null),
  seafarer_name: Jo<PERSON>.string().required(),
  seafarer_rank: Joi.string().required(),
  seafarer_rank_id: Joi.number().allow(null),
  seafarer_rank_sort_order: Joi.string().allow(null),
});

export const updateTeamMembersSchema = Joi.object({
  team_members: Joi.array().items(teamMemberSchema).required(),
});
