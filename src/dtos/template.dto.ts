import * as Joi from 'joi';
import {RiskTemplateStatus} from '../enums';
import {GetTemplateListPayload} from 'template.types';

export const getTemplateListSchema = Joi.object<GetTemplateListPayload>({
  search: Joi.string().optional(),
  status: Joi.number()
    .valid(RiskTemplateStatus.DRAFT, RiskTemplateStatus.PUBLISHED)
    .default(RiskTemplateStatus.PUBLISHED)
    .description('Risk status (1: Draft, 2: Published)'),
  created_by: Joi.array().items(Joi.string()).optional(),
  created_at: Joi.object({
    start_date: Joi.date().iso().optional(),
    end_date: Joi.date().iso().optional(),
  }).optional(),
  ra_categories: Joi.array()
    .items(Joi.number().integer().positive())
    .optional(),
  hazard_categories: Joi.array()
    .items(Joi.number().integer().positive())
    .optional(),
  sort_by: Joi.string()
    .valid('created_at', 'updated_at', 'task_requiring_ra')
    .optional()
    .default('created_at'),
  sort_order: Joi.string().valid('ASC', 'DESC').optional().default('ASC'),
  page: Joi.number().integer().min(1).optional().default(1),
  limit: Joi.number().integer().min(1).optional().default(10),
});
