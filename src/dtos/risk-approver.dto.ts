import Joi from 'joi';
import {RiskAssessmentRALevel} from '../enums/ra-level.enum';

export interface RiskApproverInput {
  risk_id: number;
  ra_level: number;
  keycloak_id: number[];
}

export const riskApproverSchema = Joi.object({
  risk_id: Joi.number().required(),
  ra_level: Joi.number().integer().min(1).max(3).required(),
  keycloak_id: Joi.array()
    .items(Joi.number().integer())
    .min(1)
    .max(3)
    .required()
    .custom((value, helpers) => {
      const {ra_level} = helpers.state.ancestors[0];

      if (ra_level === 1 && value.length !== 1) {
        return helpers.error('custom.ra_level1');
      }

      if ((ra_level === 2 || ra_level === 3) && value.length !== 3) {
        return helpers.error('custom.ra_level2or3');
      }

      return value;
    })
    .messages({
      'custom.ra_level1': 'For ra_level 1, exactly 1 approver is required.',
      'custom.ra_level2or3':
        'For ra_level 2 or 3, exactly 3 approvers are required.',
    }),
});

export function validateRiskApprover(input: RiskApproverInput): string[] {
  const {error} = riskApproverSchema.validate(input, {abortEarly: false});
  if (!error) return [];
  return error.details.map(detail => detail.message);
}

export interface UpdateRiskApproverStatusInput {
  risk_id: number;
  status: number;
  message?: string;
}

export const updateRiskApproverStatusSchema = Joi.object({
  risk_id: Joi.number().required(),
  status: Joi.number().valid(1, 2, 3).required().messages({
    'any.only':
      'Status must be one of: 1 (APPROVED), 2 (REJECTED), 3 (CONDITIONALLY_APPROVED)',
  }),
  message: Joi.string()
    .when('status', {
      is: Joi.number().valid(2, 3),
      then: Joi.string().required(),
      otherwise: Joi.string().optional(),
    })
    .messages({
      'any.required':
        'Message is required when status is REJECTED or CONDITIONALLY_APPROVED',
    }),
});

export function validateUpdateRiskApproverStatus(
  input: UpdateRiskApproverStatusInput,
): string[] {
  const {error} = updateRiskApproverStatusSchema.validate(input, {
    abortEarly: false,
  });
  if (!error) return [];
  return error.details.map(detail => detail.message);
}

export interface UpdateRiskApproversInput {
  ra_level: RiskAssessmentRALevel;
  approvers: number[];
}

export const updateRiskApproversSchema = Joi.object({
  ra_level: Joi.number()
    .valid(
      ...Object.values(RiskAssessmentRALevel).filter(
        v => typeof v === 'number',
      ),
    )
    .required()
    .messages({
      'any.only': 'RA level must be one of: ROUTINE, SPECIAL, or CRITICAL',
    }),
  // Uncomment and adjust if you want to validate approvers array as well
  // approvers: Joi.array()
  //   .items(Joi.number().integer().required())
  //   .min(1)
  //   .required()
  //   .messages({
  //     'array.min': 'At least one approver is required',
  //     'array.base': 'Approvers must be an array of numbers',
  //   }),
});

export function validateUpdateRiskApprovers(
  input: UpdateRiskApproversInput,
): string[] {
  const {error} = updateRiskApproversSchema.validate(input, {
    abortEarly: false,
  });
  if (!error) return [];
  return error.details.map(detail => detail.message);
}
