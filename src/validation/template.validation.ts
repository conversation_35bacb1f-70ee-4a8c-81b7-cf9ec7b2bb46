import Joi from 'joi';
import {
  ITemplateCreate,
  ITemplateCategory,
  ITemplateHazard,
  ITemplateJob,
  ITemplateJobRiskRating,
  ITemplateTaskReliabilityAssessment,
  IParameter,
} from '../types/template.types';

const riskParameterSchema = Joi.object<ITemplateJobRiskRating>({
  parameter_type_id: Joi.number().required().messages({
    'any.required': 'Parameter type ID is required',
    'number.base': 'Parameter type ID must be a number',
  }),
  rating: Joi.string().max(2).required().messages({
    'any.required': 'Rating is required',
    'string.base': 'Rating must be a string',
  }),
  reason: Joi.string().optional().max(255).messages({
    'string.base': 'Reason must be a string',
  }),
});

const jobSchema = Joi.object<ITemplateJob>({
  job_step: Joi.string().max(255).required().messages({
    'any.required': 'Job step is required',
    'string.base': 'Job step must be a string',
  }),
  job_hazard: Joi.string().max(255).required().messages({
    'any.required': 'Job hazard is required',
    'string.base': 'Job hazard must be a string',
  }),
  job_nature_of_risk: Joi.string().max(255).required().messages({
    'any.required': 'Job nature of risk is required',
    'string.base': 'Job nature of risk must be a string',
  }),
  job_existing_control: Joi.string().max(4000).required().messages({
    'any.required': 'Job existing control is required',
    'string.base': 'Job existing control must be a string',
  }),
  job_additional_mitigation: Joi.string().max(4000).required().messages({
    'any.required': 'Job additional mitigation is required',
    'string.base': 'Job additional mitigation must be a string',
  }),
  job_close_out_date: Joi.date().optional().messages({
    'any.required': 'Job close out date is required',
    'date.base': 'Job close out date must be a valid date',
  }),
  job_close_out_responsibility_id: Joi.string().optional().messages({
    'any.required': 'Job close out responsibility ID is required',
    'string.base': 'Job close out responsibility ID must be a string',
  }),
  template_job_initial_risk_rating: Joi.array()
    .items(riskParameterSchema)
    .required(),
  template_job_residual_risk_rating: Joi.array()
    .items(riskParameterSchema)
    .required(),
});

const categorySchema = Joi.object<ITemplateCategory>({
  is_other: Joi.boolean().optional().messages({
    'boolean.base': 'Is other must be a boolean',
  }),
  category_id: Joi.array().items(Joi.number()).min(1).required().messages({
    'any.required': 'Category IDs are required',
    'array.base': 'Category IDs must be an array',
  }),
  value: Joi.string().max(255).when('is_other', {
    is: true,
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),
});

const hazardSchema = Joi.object<ITemplateHazard>({
  is_other: Joi.boolean().required().messages({
    'any.required': 'Is other is required',
    'boolean.base': 'Is other must be a boolean',
  }),

  hazard_id: Joi.alternatives().conditional('is_other', {
    is: false,
    then: Joi.array().items(Joi.number()).min(1).required().messages({
      'any.required': 'Hazard IDs are required when "is_other" is false',
      'array.base': 'Hazard IDs must be an array',
    }),
    otherwise: Joi.array().items(Joi.number()).min(0).optional(),
  }),

  value: Joi.string()
    .max(255)
    .when('is_other', {
      is: true,
      then: Joi.required().messages({
        'any.required': 'Value is required when "is_other" is true',
      }),
      otherwise: Joi.optional(),
    }),
});

const reliabilityAssessmentSchema =
  Joi.object<ITemplateTaskReliabilityAssessment>({
    task_reliability_assessment_id: Joi.number().required().messages({
      'any.required': 'Task reliability assessment ID is required',
      'number.base': 'Task reliability assessment ID must be a number',
    }),

    task_reliability_assessment_answer: Joi.string()
      .valid('Yes', 'No', 'NA')
      .insensitive()
      .required()
      .messages({
        'any.required': 'Task reliability assessment answer is required',
        'string.base': 'Task reliability assessment answer must be a string',
        'any.only': 'Answer must be Yes, No, or NA',
      }),

    condition: Joi.alternatives().conditional(
      'task_reliability_assessment_answer',
      {
        is: Joi.string().valid('yes').insensitive(),
        then: Joi.string().max(255).required().messages({
          'any.required': 'Condition is required when the answer is yes',
          'string.base': 'Condition must be a string',
        }),
        otherwise: Joi.forbidden().messages({
          'any.unknown': 'Condition is only allowed when the answer is yes',
        }),
      },
    ),
  });

const parameterSchema = Joi.object<IParameter>({
  is_other: Joi.boolean().required().messages({
    'any.required': 'Is other is required',
    'boolean.base': 'Is other must be a boolean',
  }),
  parameter_type_id: Joi.number().required().messages({
    'any.required': 'Parameter type ID is required',
    'number.base': 'Parameter type ID must be a number',
  }),

  parameter_id: Joi.alternatives().conditional('is_other', {
    is: false,
    then: Joi.array().items(Joi.number()).min(1).required().messages({
      'any.required': 'Parameter IDs are required when "is_other" is false',
      'array.base': 'Parameter IDs must be an array',
    }),
    otherwise: Joi.array().items(Joi.number()).min(0).optional(),
  }),

  value: Joi.string()
    .max(255)
    .when('is_other', {
      is: true,
      then: Joi.required().messages({
        'any.required': 'Value is required when "is_other" is true',
      }),
      otherwise: Joi.optional(),
    }),
});

// export const validateTemplate = (
//   template: ITemplateCreate,
//   isPublish: boolean,
// ): string[] => {
//   const baseSchema = {
//     task_requiring_ra: Joi.string().required().max(255).messages({
//       'any.required': 'Task requiring RA is required',
//       'string.base': 'Task requiring RA must be a string',
//     }),
//     task_duration: Joi.string().required().max(255).messages({
//       'any.required': 'Task duration is required',
//       'string.base': 'Task duration must be a string',
//     }),
//     task_alternative_consideration: Joi.string().max(4000).allow('').messages({
//       'string.base': 'Task alternative consideration must be a string',
//       'string.max':
//         'Task alternative consideration must not exceed 4000 characters',
//     }),
//     task_rejection_reason: Joi.string().max(4000).allow('').messages({
//       'string.base': 'Task rejection reason must be a string',
//     }),
//     worst_case_scenario: Joi.string().max(4000).allow('').messages({
//       'string.base': 'Worst case scenario must be a string',
//       'string.max': 'Worst case scenario must not exceed 4000 characters',
//     }),
//     recovery_measures: Joi.string().max(4000).allow('').messages({
//       'string.base': 'Recovery measures must be a string',
//       'string.max': 'Recovery measures must not exceed 4000 characters',
//     }),
//     status: Joi.string().valid('DRAFT', 'PUBLISHED').required().messages({
//       'any.required': 'Status is required',
//       'string.base': 'Status must be a string',
//       'any.only': 'Status must be one of DRAFT, PUBLISHED',
//     }),
//     template_category: categorySchema,
//     template_hazard: hazardSchema,
//     parameters: Joi.array().items(parameterSchema).required(),
//     template_job: Joi.array().items(jobSchema),
//     template_task_reliability_assessment: Joi.array().items(
//       reliabilityAssessmentSchema,
//     ),
//     template_keyword: Joi.array()
//       .items(
//         Joi.string().max(255).required().messages({
//           'any.required': 'Keyword is required',
//           'string.base': 'Keyword must be a string',
//         }),
//       )
//       .optional()
//       .messages({
//         'array.base': 'template_keyword must be an array of strings',
//         'any.required': 'template_keyword is required',
//       }),
//     draft_step: Joi.number()
//       .min(1)
//       .max(7)
//       .strict()
//       .when('status', {
//         is: 'DRAFT',
//         then: Joi.required().messages({
//           'any.required': 'draft_step is required when status is DRAFT',
//         }),
//         otherwise: Joi.optional(),
//       })
//       .messages({
//         'number.base': 'draft_step must be number',
//         'number.min': 'draft_step is allowed between 1 to 7',
//         'number.max': 'draft_step is allowed between 1 to 7',
//       }),
//   };

//   const schema = isPublish
//     ? Joi.object<ITemplateCreate>(baseSchema).fork(
//         Object.keys(baseSchema).filter(key => key !== 'status'),
//         schema => schema.required(),
//       )
//     : Joi.object<ITemplateCreate>(baseSchema);

//   const {error} = schema.validate(template, {abortEarly: false});
//   return error ? error.details.map(detail => detail.message) : [];
// };

export const validateTemplate = (
  template: ITemplateCreate,
  isPublish: boolean,
): string[] => {
  const baseSchema = {
    task_requiring_ra: Joi.string().required().max(255).messages({
      'any.required': 'Task requiring RA is required',
      'string.base': 'Task requiring RA must be a string',
    }),
    task_duration: Joi.string().required().max(255).messages({
      'any.required': 'Task duration is required',
      'string.base': 'Task duration must be a string',
    }),
    task_alternative_consideration: Joi.string().max(4000).allow('').messages({
      'string.base': 'Task alternative consideration must be a string',
      'string.max':
        'Task alternative consideration must not exceed 4000 characters',
    }),
    task_rejection_reason: Joi.string().max(4000).allow('').messages({
      'string.base': 'Task rejection reason must be a string',
    }),
    worst_case_scenario: Joi.string().max(4000).allow('').messages({
      'string.base': 'Worst case scenario must be a string',
      'string.max': 'Worst case scenario must not exceed 4000 characters',
    }),
    recovery_measures: Joi.string().max(4000).allow('').messages({
      'string.base': 'Recovery measures must be a string',
      'string.max': 'Recovery measures must not exceed 4000 characters',
    }),
    status: Joi.string().valid('DRAFT', 'PUBLISHED').required().messages({
      'any.required': 'Status is required',
      'string.base': 'Status must be a string',
      'any.only': 'Status must be one of DRAFT, PUBLISHED',
    }),
    template_category: categorySchema,
    template_hazard: hazardSchema,
    parameters: Joi.array().items(parameterSchema),
    template_job: Joi.array().items(jobSchema),
    template_task_reliability_assessment: Joi.array().items(
      reliabilityAssessmentSchema,
    ),
    template_keyword: Joi.array()
      .items(
        Joi.string().max(255).required().messages({
          'any.required': 'Keyword is required',
          'string.base': 'Keyword must be a string',
        }),
      )
      .optional()
      .messages({
        'array.base': 'template_keyword must be an array of strings',
        'any.required': 'template_keyword is required',
      }),
    draft_step: Joi.number()
      .min(1)
      .max(7)
      .strict()
      .when('status', {
        is: 'DRAFT',
        then: Joi.required().messages({
          'any.required': 'draft_step is required when status is DRAFT',
        }),
        otherwise: Joi.optional(),
      })
      .messages({
        'number.base': 'draft_step must be number',
        'number.min': 'draft_step is allowed between 1 to 7',
        'number.max': 'draft_step is allowed between 1 to 7',
      }),
  };

  // If status is DRAFT, make all fields optional except task_requiring_ra and task_duration and draft_step
  let schema;
  if (!isPublish && template.status === 'DRAFT') {
    schema = Joi.object<ITemplateCreate>({
      ...baseSchema,
      // Only keep task_requiring_ra, task_duration, draft_step as required, rest optional
      task_requiring_ra: baseSchema.task_requiring_ra,
      task_duration: baseSchema.task_duration,
      draft_step: baseSchema.draft_step,
      task_alternative_consideration:
        baseSchema.task_alternative_consideration.optional(),
      task_rejection_reason: baseSchema.task_rejection_reason.optional(),
      worst_case_scenario: baseSchema.worst_case_scenario.optional(),
      recovery_measures: baseSchema.recovery_measures.optional(),
      status: baseSchema.status,
      template_category: baseSchema.template_category.optional(),
      template_hazard: baseSchema.template_hazard.optional(),
      parameters: baseSchema.parameters.optional(),
      template_job: baseSchema.template_job.optional(),
      template_task_reliability_assessment:
        baseSchema.template_task_reliability_assessment.optional(),
      template_keyword: baseSchema.template_keyword.optional(),
    });
  } else {
    // For publish, all required except status
    schema = isPublish
      ? Joi.object<ITemplateCreate>(baseSchema).fork(
          Object.keys(baseSchema).filter(key => key !== 'status'),
          s => s.required(),
        )
      : Joi.object<ITemplateCreate>(baseSchema);
  }

  const {error} = schema.validate(template, {abortEarly: false});
  return error ? error.details.map(detail => detail.message) : [];
};
