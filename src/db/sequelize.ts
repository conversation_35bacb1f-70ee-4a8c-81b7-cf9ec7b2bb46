import {Sequelize} from 'sequelize';
import dotenv from 'dotenv';
dotenv.config();

const dbUri = process.env.DB_URI ?? '';
export const RISK_DB = dbUri?.split('/').pop() ?? 'risk-assessment';

const ssl =
  process.env.PG_SSL_ENABLED === 'true' || process.env.PG_SSL_ENABLED === 'TRUE'
    ? {require: true, rejectUnauthorized: false}
    : undefined;

export const sequelize = new Sequelize({
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  port: Number(process.env.DB_PORT),
  dialect: 'postgres',
  dialectOptions: {
    ssl: ssl,
    // Add connection timeout and keep alive settings
    connectTimeout: 60000, // 60 seconds
    socketTimeout: 60000,  // 60 seconds
    keepAlive: true,
    keepAliveInitialDelayMillis: 0,
  },
  pool: {
    max: 1,        // Single connection per Lambda container
    min: 0,        // No minimum connections for Lambda
    acquire: 5000,  // Reduced to 5 seconds - fail fast if no connections
    idle: 300000,   // 5 minutes idle timeout for Lambda reuse
    evict: 60000,   // More aggressive eviction - every 30 seconds
    validate: (client: any) => {
      // Simple validation - check if connection is still alive
      return client && !client.connection?.destroyed;
    },
  },
  retry: {
    max: 3,        // Retry failed operations up to 3 times
  },
  logging: false,
});

export default sequelize;
