import 'pg';
import sequelize, {R<PERSON>K_DB} from './sequelize';
import associateModels from '../models/association';
import {logger} from '../utils/logger';
import {QueryTypes} from 'sequelize';

// Export sequelize instance for backward compatibility
export const sq = sequelize;
export {RISK_DB};

// Connection state tracking
let initialized = false;
let isClosing = false;

// Circuit breaker for connection failures
let connectionFailures = 0;
let lastFailureTime = 0;
const MAX_FAILURES = 3;
const CIRCUIT_BREAKER_TIMEOUT = 30000; // 30 seconds

const isCircuitBreakerOpen = (): boolean => {
  if (connectionFailures >= MAX_FAILURES) {
    const timeSinceLastFailure = Date.now() - lastFailureTime;
    if (timeSinceLastFailure < CIRCUIT_BREAKER_TIMEOUT) {
      return true; // Circuit is open, reject requests
    } else {
      // Reset circuit breaker after timeout
      connectionFailures = 0;
      return false;
    }
  }
  return false;
};

// For Lambda, we don't close Sequelize connections manually
// AWS will clean them up when the container is destroyed

export const initDb = async () => {
  // Check circuit breaker first
  if (isCircuitBreakerOpen()) {
    logger.warn({
      group: 'database',
      name: 'circuit_breaker',
      message: `Database circuit breaker is open. Rejecting connection attempt. Failures: ${connectionFailures}`,
    });
    throw new Error('Database service temporarily unavailable due to repeated connection failures');
  }

  // If already initialized, trust it for Lambda container reuse
  if (initialized && !isClosing) {
    logger.debug({
      group: 'database',
      name: 'connection',
      message: 'Database connection already initialized, reusing for Lambda container',
    });
    return;
  }

  if (isClosing) {
    logger.debug({
      group: 'database',
      name: 'connection',
      message: 'Skipping initDb - connection is closing',
    });
    return;
  }

  try {
    // Single authenticate call - let Sequelize pool handle connection health
    await sequelize.authenticate();
    associateModels();

    // Don't sync models on every init in production
    if (process.env.NODE_ENV !== 'production') {
      await sequelize.sync();
    }
    initialized = true;

    try {
      const connectionStats = await sequelize.query(
        `
          SELECT
            count(*) as total_connections,
            state,
            application_name,
            CASE
              WHEN state = 'idle' AND state_change < NOW() - INTERVAL '5 minutes' THEN 'stale_idle'
              ELSE state
            END as connection_health
          FROM pg_stat_activity
          WHERE application_name IS NOT NULL
          GROUP BY state, application_name, connection_health
          ORDER BY total_connections DESC;
        `,
        {type: QueryTypes.SELECT},
      );

      // Check for concerning connection patterns
      const idleConnections = connectionStats.filter((stat: any) => stat.state === 'idle');
      const staleConnections = connectionStats.filter((stat: any) => stat.connection_health === 'stale_idle');

      if (idleConnections.length > 0 && (idleConnections[0] as any).total_connections > 10) {
        logger.warn({
          group: 'database',
          name: 'connection_warning',
          message: `High number of idle connections detected: ${(idleConnections[0] as any).total_connections}`,
        });
      }

      if (staleConnections.length > 0) {
        logger.warn({
          group: 'database',
          name: 'stale_connections',
          message: `Stale idle connections detected: ${JSON.stringify(staleConnections)}`,
        });
      }

      logger.info({
        group: 'database',
        name: 'connection_stats',
        message: `Current database connections: ${JSON.stringify(connectionStats)}`,
      });
    } catch (statsErr) {
      logger.warn({
        group: 'database',
        name: 'connection_stats_error',
        message: `Could not fetch connection statistics: ${statsErr instanceof Error ? statsErr.message : 'Unknown error'}`,
      });
    }

    logger.info({
      group: 'database',
      name: 'connection',
      message: 'Database connection initialized successfully',
    });
  } catch (err) {
    initialized = false; // Reset initialization state on error

    // Track failures for circuit breaker
    connectionFailures++;
    lastFailureTime = Date.now();

    const errorMessage = err instanceof Error ? err.message : 'Unknown database error';
    logger.error({
      group: 'database',
      name: 'connection_error',
      message: `${errorMessage} (Failure count: ${connectionFailures})`,
    });

    // Check if it's a connection termination error and provide specific guidance
    if (errorMessage.includes('Connection terminated unexpectedly') ||
        errorMessage.includes('connection was closed') ||
        errorMessage.includes('Connection lost') ||
        errorMessage.includes('remaining connection slots are reserved')) {
      logger.error({
        group: 'database',
        name: 'connection_termination',
        message: 'Database connection was terminated. Circuit breaker may activate if failures continue.',
      });
    }

    throw err;
  }
};

export const closeDb = async () => {
  // Option 1: Keep connections open (current approach - recommended)
  if (process.env.FORCE_CLOSE_CONNECTIONS !== 'true') {
    logger.debug({
      group: 'database',
      name: 'connection',
      message: 'Keeping database connection open (Lambda best practice)',
    });
    return;
  }

  // Option 2: Force close connections (use only if connection exhaustion persists)
  try {
    if (initialized) {
      await sequelize.close();
      initialized = false;
      connectionFailures = 0; // Reset circuit breaker on successful close
      logger.info({
        group: 'database',
        name: 'connection',
        message: 'Database connections closed successfully',
      });
    }
  } catch (err) {
    logger.error({
      group: 'database',
      name: 'connection_close_error',
      message: `Error closing database connections: ${err instanceof Error ? err.message : 'Unknown error'}`,
    });
  }
};
