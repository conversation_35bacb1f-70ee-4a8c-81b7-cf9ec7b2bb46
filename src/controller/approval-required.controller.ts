import {Op} from 'sequelize';
import {ApprovalRequiredAttributes} from '../types/approval-required.types';
import ApprovalRequired from '../models/approval-required.model';

class ApprovalRequiredController {
  static async list(search: string): Promise<ApprovalRequiredAttributes[]> {
    try {
      const whereClause: any = {};
      if (search.trim()) {
        whereClause.name = {
          [Op.iLike]: `%${search.trim()}%`,
        };
      }
      const approvalRequired = await ApprovalRequired.findAll({
        where: whereClause,
        attributes: ['id', 'name'],
        order: [['name', 'ASC']],
        raw: true,
      });
      return approvalRequired;
    } catch (error: any) {
      console.error('Error fetching approval required:', error);
      throw error;
    }
  }
}

export default ApprovalRequiredController;
