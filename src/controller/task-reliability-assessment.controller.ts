import {Op} from 'sequelize';
import {TaskReliabilityAssessmentAttributes} from '../types/task-reliability-assessment';
import TaskReliabilityAssessment from '../models/task-reliability-assessment.model';

class TaskReliabilityAssessmentController {
  static async list(
    search: string,
  ): Promise<TaskReliabilityAssessmentAttributes[]> {
    try {
      const whereClause = search.trim()
        ? {
            [Op.or]: [
              {
                name: {
                  [Op.iLike]: `%${search.trim()}%`,
                },
              },
            ],
          }
        : {};

      const assessments = await TaskReliabilityAssessment.findAll({
        where: whereClause,
        attributes: ['id', 'name', 'options'],
        // order: [['name', 'ASC']],
        raw: true,
      });
      return assessments;
    } catch (error: any) {
      console.error('Error fetching task reliability assessments:', error);
      throw error;
    }
  }
}

export default TaskReliabilityAssessmentController;
