import {Op} from 'sequelize';
import {HazardAttributes} from '../types/hazards';
import Hazard from '../models/hazard.model';

class HazardsController {
  static async list(search: string): Promise<HazardAttributes[]> {
    try {
      const whereClause: any = {};
      if (search.trim()) {
        whereClause.name = {
          [Op.iLike]: `%${search.trim()}%`,
        };
      }
      const hazards = await Hazard.findAll({
        where: whereClause,
        attributes: ['id', 'name'],
        order: [['name', 'ASC']],
        raw: true,
      });
      return hazards;
    } catch (error: any) {
      console.error('Error fetching hazards:', error);
      throw error;
    }
  }
}

export default HazardsController;
