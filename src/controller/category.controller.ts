import {Op} from 'sequelize';
import {CategoryAttributes} from '../types/category.types';
import Category from '../models/category.model';

class CategoryController {
  static async list(search: string): Promise<CategoryAttributes[]> {
    try {
      const whereClause: any = {};
      if (search.trim()) {
        whereClause.name = {
          [Op.iLike]: `%${search.trim()}%`,
        };
      }
      const categories = await Category.findAll({
        where: whereClause,
        attributes: ['id', 'name'],
        order: [['name', 'ASC']],
        raw: true,
      });
      return categories;
    } catch (error: any) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }
}

export default CategoryController;
