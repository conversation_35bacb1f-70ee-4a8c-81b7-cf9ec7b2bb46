import {Op, FindOptions} from 'sequelize';
import {ParameterTypeAttributes} from '../types/parameter-type.types';
import ParameterType from '../models/parameter-type.model';

class ParameterTypeController {
  static async list(
    search: string,
    isRequiredForRiskRating?: boolean,
  ): Promise<ParameterTypeAttributes[]> {
    try {
      const whereClause: any = {};
      if (search.trim()) {
        whereClause.name = {
          [Op.iLike]: `%${search.trim()}%`,
        };
      }
      const query: FindOptions<ParameterTypeAttributes> = {
        where: whereClause,
        attributes: ['id', 'name'],
        order: [['name', 'ASC']] as any,
        raw: true,
      };

      if (isRequiredForRiskRating) {
        query.where = {
          ...query.where,
          is_required_for_risk_rating: isRequiredForRiskRating,
        };
      }

      const parameterTypes = await ParameterType.findAll(query);
      return parameterTypes;
    } catch (error: any) {
      console.error('Error fetching parameter types:', error);
      throw error;
    }
  }
}

export default ParameterTypeController;
