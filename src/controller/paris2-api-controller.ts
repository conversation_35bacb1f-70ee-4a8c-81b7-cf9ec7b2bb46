import axios from 'axios';

import qs from 'qs';

class Paris2Controller {
  /**
   * Retrieves an access token from the authentication server using OpenID Connect.
   *
   * This method sends a POST request to the OpenID Connect token endpoint with the required
   * credentials and client information. The credentials and client details are sourced from
   * environment variables. The response contains an access token, which is returned upon
   * successful authentication.
   *
   * @returns {Promise<string | undefined>} A promise that resolves to the access token as a string
   * if the request is successful, or `undefined` if an error occurs.
   *
   * @throws {Error} Logs an error message to the console if the request fails.
   *
   * @example
   * ```typescript
   * const token = await getAccessToken();
   * ```
   */
  getAccessToken = async () => {
    const data = qs.stringify({
      username: process.env.OPEN_ID_USERNAME,
      password: process.env.OPEN_ID_PASSWORD,
      client_secret: process.env.OPEN_ID_CLIENT_SECRET,
      grant_type: process.env.OPEN_ID_GRANT_TYPE,
      client_id: process.env.OPEN_ID_CLIENT_ID,
    });
    const baseUrl = process.env.PARIS2_AUTH_BASE_URL;
    const config = {
      method: 'post',
      url: `${baseUrl}/realms/paris2/protocol/openid-connect/token`,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: data,
    };
    try {
      const response = await axios.request(config);
      const token = response.data.access_token;

      return token;
    } catch (error) {
      return;
    }
  };

  /**
   * Fetches user details for the given user IDs.
   *
   * @param userIds - A string containing the user IDs to fetch details for.
   * @returns A promise that resolves to the user details.
   * @throws Will throw an error if unable to fetch the access token or if the request fails.
   */
  getUserDetails = async (userIds: string[]): Promise<any> => {
    const token = await this.getAccessToken();
    if (!token) {
      throw new Error('Unable to fetch access token');
    }

    const queryString = userIds.map(id => `userId=${id}`).join('&');
    const baseUrl = process.env.PARIS2_BASE_URL;
    const config = {
      method: 'get',
      url: `${baseUrl}keycloak-admin/users?${queryString}`,
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };

    try {
      const response = await axios.request(config);
      console.log('User details fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch user details');
    }
  };
  /**
   * Fetches vessel ownerships with optional keyword search.
   *
   * @param keyword - Optional search keyword to filter vessels
   * @returns A promise that resolves to the vessel ownerships data
   * @throws Will throw an error if unable to fetch the access token or if the request fails
   */
  getVesselOwnerships = async (keyword?: string): Promise<any> => {
    const token = await this.getAccessToken();
    if (!token) {
      throw new Error('Unable to fetch access token');
    }

    const params = {
      limit: 700,
      offset: 0,
      order: 'name asc',
      status: ['active', 'pending_handover', 'handed_over'],
      flatten: true,
      ...(keyword && {keyword}),
    };

    const fields = [
      'vessel_ownership.id',
      'vessel.id',
      'vessel_ownership.vessel_account_code_new',
      'vessel.status',
    ];

    const queryString = require('qs').stringify(
      {...params, f: fields},
      {arrayFormat: 'repeat'},
    );
    const baseUrl = process.env.PARIS2_BASE_URL;
    const config = {
      method: 'get',
      url: `${baseUrl}vessel/v2/ownerships?${queryString}`,
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch vessel ownerships');
    }
  };
  /**
   * Fetches crew list data for a specific vessel and month
   *
   * @param vesselId - The ID of the vessel to fetch crew list for
   * @param month - Optional month in YYYY-MM format. Defaults to current month if not provided
   * @returns A promise that resolves to the crew list data
   * @throws Will throw an error if unable to fetch the access token or if the request fails
   */
  getCrewList = async (vesselId: number, month?: string): Promise<any> => {
    const token = await this.getAccessToken();
    if (!token) {
      throw new Error('Unable to fetch access token');
    }

    // Get current month in YYYY-MM format if month not provided
    const currentMonth = month ?? new Date().toISOString().slice(0, 7);

    const baseUrl = process.env.PARIS2_BASE_URL;
    const config = {
      method: 'get',
      url: `${baseUrl}crew-assignment/crew-list?vessel_id=${vesselId}&crew_list_status_month=${currentMonth}`,
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    };

    try {
      const response = await axios.request(config);
      const filteredCrewList = (response.data?.results || []).map(
        (crew: any) => {
          const statusHistory =
            crew.seafarer_person?.seafarer_status_history?.[0];
          return {
            seafarer_id: crew.id,
            seafarer_person_id: crew.seafarer_person_id,
            seafarer_hkid: crew.hkid,
            seafarer_rank_id: crew.rank_id,
            seafarer_name: `${crew.seafarer_person?.first_name} ${crew.seafarer_person?.last_name}`,
            seafarer_rank: statusHistory?.seafarer_rank?.value,
            seafarer_rank_sort_order:
              statusHistory?.seafarer_rank?.sortpriority,
          };
        },
      );
      return filteredCrewList;
    } catch (error) {
      throw new Error('Failed to fetch crew list');
    }
  };
  /**
   * Fetches the list of reporting offices.
   *
   * @returns A promise that resolves to the reporting offices data
   * @throws Will throw an error if unable to fetch the access token or if the request fails
   */
  getReportingOffices = async (): Promise<any> => {
    const token = await this.getAccessToken();
    if (!token) {
      throw new Error('Unable to fetch access token');
    }

    const baseUrl = process.env.PARIS2_BASE_URL;
    const config = {
      method: 'get',
      url: `${baseUrl}seafarer/get-reporting-offices`,
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch reporting offices');
    }
  };

  /**
   * Fetches vessel ownership details by ID.
   *
   * @param id - The ID of the vessel ownership to fetch
   * @returns A promise that resolves to the vessel ownership data
   * @throws Will throw an error if unable to fetch the access token or if the request fails
   */
  getVesselOwnershipById = async (id: number): Promise<any> => {
    const token = await this.getAccessToken();
    if (!token) {
      throw new Error('Unable to fetch access token');
    }

    const baseUrl = process.env.PARIS2_BASE_URL;
    const config = {
      method: 'get',
      url: `${baseUrl}vessel/v2/ownerships/${id}`,
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch vessel ownership details');
    }
  };
}

export default new Paris2Controller();
