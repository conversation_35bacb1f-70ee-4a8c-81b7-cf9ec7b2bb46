import {Op} from 'sequelize';
import {ParameterAttributes} from '../types/parameter.types';
import Parameter from '../models/parameter.model';
import ParameterType from '../models/parameter-type.model';

class ParameterController {
  static async list(search: string): Promise<ParameterAttributes[]> {
    try {
      const whereClause: any = {};
      if (search.trim()) {
        whereClause.name = {
          [Op.iLike]: `%${search.trim()}%`,
        };
      }

      return Parameter.findAll({
        where: whereClause,
        attributes: ['id', 'parameter_type_id', 'name'],
        include: [
          {
            model: ParameterType,
            as: 'parameter_type',
            attributes: ['id', 'name'],
          },
        ],
        order: [['name', 'ASC']],
        raw: true,
        nest: true,
      });
    } catch (error: any) {
      console.error('Error fetching parameters:', error);
      throw error;
    }
  }
}

export default ParameterController;
