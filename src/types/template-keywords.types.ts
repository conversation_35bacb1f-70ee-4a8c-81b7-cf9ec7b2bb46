import {BasicStatus} from '../enums';

export interface ITemplateKeywordAttributes {
  id?: number;
  template_id: number;
  name: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateKeywordAttributes = ITemplateKeywordAttributes;

export type CreateTemplateKeywordInput = Omit<
  TemplateKeywordAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateKeywordInput = Partial<CreateTemplateKeywordInput>;
