import {BasicStatus} from '../enums';

export interface ITemplateCategoryAttributes {
  id: number;
  template_id: number;
  category_id: number;
  category_is_other: boolean;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateCategoryAttributes = ITemplateCategoryAttributes;

export type CreateTemplateCategoryInput = Omit<
  TemplateCategoryAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateCategoryInput = Partial<CreateTemplateCategoryInput>;
