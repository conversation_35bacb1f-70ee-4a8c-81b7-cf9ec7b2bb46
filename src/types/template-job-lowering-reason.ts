import {BasicStatus} from '../enums';

export interface ITemplateJobLoweringReasonAttributes {
  id: number;
  template_job_id: number;
  parameter_id: number;
  lowering_reason: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateJobLoweringReasonAttributes =
  ITemplateJobLoweringReasonAttributes;

export type CreateTemplateJobLoweringReasonInput = Omit<
  TemplateJobLoweringReasonAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateJobLoweringReasonInput =
  Partial<CreateTemplateJobLoweringReasonInput>;
