import {MasterDataType, BasicStatus} from '../enums';

export interface ICategoryAttributes {
  id: number;
  name: string;
  status: BasicStatus;
  type: MasterDataType;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type CategoryAttributes = ICategoryAttributes;

export type CreateCategoryInput = Omit<
  CategoryAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateCategoryInput = Partial<CreateCategoryInput>;
