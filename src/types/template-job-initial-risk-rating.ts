import {BasicStatus} from '../enums';

export interface ITemplateJobInitialRiskRatingAttributes {
  id: number;
  template_job_id: number;
  parameter_type_id: number;
  rating: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateJobInitialRiskRatingAttributes =
  ITemplateJobInitialRiskRatingAttributes;

export type CreateTemplateJobInitialRiskRatingInput = Omit<
  TemplateJobInitialRiskRatingAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateJobInitialRiskRatingInput =
  Partial<CreateTemplateJobInitialRiskRatingInput>;
