import {APIGatewayProxyEvent, Context} from 'aws-lambda';
import {AllowedRole} from '../handlers/auth';

export interface HttpApiRequestContext {
  http: {
    method: string;
    path: string;
    protocol: string;
    sourceIp: string;
    userAgent: string;
  };
  authorizer?: {
    user_id?: string;
    user_name?: string;
    ship_party_id?: string;
    scope?: string;
    userAccess?: string;
    ship_party_type?: string;
    accessible_ship_party_ids?: string[];
  };
}

export interface LambdaEvent
  extends Omit<
    APIGatewayProxyEvent,
    | 'body'
    | 'pathParameters'
    | 'queryStringParameters'
    | 'multiValueQueryStringParameters'
    | 'requestContext'
  > {
  body?: string;
  encoding?: string;
  isBase64Encoded: boolean;
  pathParameters?: {
    [key: string]: string;
  };
  queryStringParameters?: {
    [key: string]: string;
  };
  multiValueQueryStringParameters?: {
    [key: string]: string[];
  };
  requestContext?:
    | HttpApiRequestContext
    | APIGatewayProxyEvent['requestContext'];
}

export type LambdaContext = Context;

export type LambdaHandler = (
  data: LambdaData,
  context: LambdaContext,
) => Promise<LambdaResponse>;

export interface LambdaConfig {
  db?: string[];
  roles?: AllowedRole[] | ((data: LambdaData) => Promise<string[]>);
  dataType?: 'file' | 'json';
  validateAccess?: (data: LambdaData) => Promise<void>;
}

export interface LambdaData {
  method?: string;
  jsonBody?: Record<string, any>;
  body?: string;
  pathParameters?: {
    [key: string]: string;
  };
  queryStringParameters?: {
    [key: string]: string;
  };
  multiValueQueryStringParameters?: {
    [key: string]: string[];
  };
  snsMessage?: string | null;
  requestId: string;
  formFields?: Record<string, string>;
  sqsMessage?: string;
  files?: FileMetadata[];
  user: {
    userAccess?: {
      userRoles: string[];
      userGroups: string[];
    };
    ship_party_id?: string;
    ship_party_type?: string;
    user_id: string;
  };
  userGroups?: string[];
  userRoles?: string[];
  ship_party_id?: string;
  ship_party_type?: string;
  accessible_ship_party_ids?: string[];
}

export interface LambdaResponse {
  statusCode?: number;
  response?: any;
  headers?: Record<string, string | boolean>;
}

export interface LambdaError extends Error {
  statusCode?: number;
}

export interface FileMetadata {
  filename: string;
  mimeType: string;
  encoding: string;
  fieldname: string;
  content?: Buffer;
}

export interface MultiFormData {
  files: FileMetadata[];
  formFields: Record<string, string>;
}
