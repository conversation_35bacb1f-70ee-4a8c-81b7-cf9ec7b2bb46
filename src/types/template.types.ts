import {RiskTemplateStatus} from '../enums';

export interface ITemplateJobRiskRating {
  parameter_type_id: number;
  rating: string;
  reason?: string;
}

export interface ITemplateJob {
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_existing_control: string;
  job_additional_mitigation: string;
  job_close_out_date: Date;
  job_close_out_responsibility_id: string;
  template_job_initial_risk_rating?: ITemplateJobRiskRating[];
  template_job_residual_risk_rating?: ITemplateJobRiskRating[];
}

export interface ITemplateCategory {
  is_other: boolean;
  category_id: number[];
  value?: string;
}

export interface ITemplateHazard {
  is_other: boolean;
  hazard_id: number[];
  value?: string;
}

export interface IParameter {
  is_other: boolean;
  parameter_type_id: number;
  parameter_id: number[];
  value?: string;
}

export interface ITemplateTaskReliabilityAssessment {
  task_reliability_assessment_id: number;
  task_reliability_assessment_answer: string;
  condition: string;
}

export interface ITemplateCreate {
  task_requiring_ra: string;
  task_duration: string;
  task_alternative_consideration?: string;
  task_rejection_reason?: string;
  worst_case_scenario?: string;
  recovery_measures?: string;
  status: string;
  draft_step?: number;
  template_category: ITemplateCategory;
  template_hazard: ITemplateHazard;
  parameters: IParameter[];
  template_job: ITemplateJob[];
  template_task_reliability_assessment: ITemplateTaskReliabilityAssessment[];
  template_keyword?: string[];
}

export interface ITemplateAttributes {
  id: number;
  task_requiring_ra: string;
  task_duration: string;
  task_alternative_consideration?: string;
  task_rejection_reason?: string;
  worst_case_scenario?: string;
  recovery_measures?: string;
  draft_step?: number;
  status: RiskTemplateStatus;
  publish_on?: Date;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateAttributes = ITemplateAttributes;

export type CreateTemplateInput = Omit<
  TemplateAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateInput = Partial<CreateTemplateInput>;

export type GetTemplateListPayload = {
  search?: string;
  sort_order?: string;
  status?: number;
  sort_by?: string;
  created_by?: string[];
  created_at?: {
    start_date?: string;
    end_date?: string;
  };
  ra_categories?: number[];
  hazard_categories?: number[];
  page?: number;
  limit?: number;
};
