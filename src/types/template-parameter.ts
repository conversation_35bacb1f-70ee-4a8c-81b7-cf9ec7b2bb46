import {BasicStatus} from '../enums';

export interface ITemplateParameterAttributes {
  id: number;
  template_id: number;
  parameter_type_id: number;
  parameter_id?: number;
  value?: string;
  parameter_is_other: boolean;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateParameterAttributes = ITemplateParameterAttributes;

export type CreateTemplateParameterInput = Omit<
  TemplateParameterAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateParameterInput =
  Partial<CreateTemplateParameterInput>;
