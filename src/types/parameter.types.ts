import {BasicStatus} from '../enums';

export interface IParameterAttributes {
  id: number;
  parameter_type_id: number;
  name: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type ParameterAttributes = IParameterAttributes;

export type CreateParameterInput = Omit<
  ParameterAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateParameterInput = Partial<CreateParameterInput>;
