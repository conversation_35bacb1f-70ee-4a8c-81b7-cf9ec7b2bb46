export interface ITaskReliabilityAssessmentAttributes {
  id: number;
  name: string;
  options: {[key: string]: any}; // JSONB field to store flexible options
  status: number;
  created_by?: string;
  updated_by?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateTaskReliabilityAssessmentInput {
  name: string;
  options: {[key: string]: any};
  status: number;
}

export interface UpdateTaskReliabilityAssessmentInput
  extends Partial<CreateTaskReliabilityAssessmentInput> {
  id: number;
}

export type TaskReliabilityAssessmentAttributes = Omit<
  ITaskReliabilityAssessmentAttributes,
  'deletedAt' | 'deletedBy'
>;
