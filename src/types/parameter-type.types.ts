import {BasicStatus} from '../enums';

export interface IParameterTypeAttributes {
  id: number;
  name: string;
  is_required_for_risk_rating: boolean;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type ParameterTypeAttributes = IParameterTypeAttributes;

export type CreateParameterTypeInput = Omit<
  ParameterTypeAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateParameterTypeInput = Partial<CreateParameterTypeInput>;
