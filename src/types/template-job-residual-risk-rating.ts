import {BasicStatus} from '../enums';

export interface ITemplateJobResidualRiskRatingAttributes {
  id: number;
  template_job_id: number;
  parameter_type_id: number;
  rating: string;
  reason: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateJobResidualRiskRatingAttributes =
  ITemplateJobResidualRiskRatingAttributes;

export type CreateTemplateJobResidualRiskRatingInput = Omit<
  TemplateJobResidualRiskRatingAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateJobResidualRiskRatingInput =
  Partial<CreateTemplateJobResidualRiskRatingInput>;
