import {BasicStatus} from '../enums';

export interface ITemplateTaskReliabilityAssessmentAttributes {
  id: number;
  template_id: number;
  task_reliability_assessment_id: number;
  task_reliability_assessment_answer: string;
  condition: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateTaskReliabilityAssessmentAttributes =
  ITemplateTaskReliabilityAssessmentAttributes;

export type CreateTemplateTaskReliabilityAssessmentInput = Omit<
  TemplateTaskReliabilityAssessmentAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateTaskReliabilityAssessmentInput =
  Partial<CreateTemplateTaskReliabilityAssessmentInput>;
