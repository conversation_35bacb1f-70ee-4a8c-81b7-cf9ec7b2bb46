import {BasicStatus} from '../enums';

export interface ITemplateJobAttributes {
  id: number;
  template_id: number;
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_existing_control: string;
  job_additional_mitigation: string;
  job_close_out_date: Date;
  job_close_out_responsibility_id: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateJobAttributes = ITemplateJobAttributes;

export type CreateTemplateJobInput = Omit<
  TemplateJobAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateJobInput = Partial<CreateTemplateJobInput>;
