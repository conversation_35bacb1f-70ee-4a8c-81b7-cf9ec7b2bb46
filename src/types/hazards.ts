export interface IHazardAttributes {
  id: number;
  name: string;
  status: number;
  created_by?: string;
  updated_by?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateHazardInput {
  name: string;
  status: number;
}

export interface UpdateHazardInput extends Partial<CreateHazardInput> {
  id: number;
}

export type HazardAttributes = Omit<
  IHazardAttributes,
  'deletedAt' | 'deletedBy'
>;
