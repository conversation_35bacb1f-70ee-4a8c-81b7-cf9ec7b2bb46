import {BasicStatus} from '../enums';

export interface ITemplateHazardAttributes {
  id: number;
  template_id: number;
  hazard_id?: number;
  hazard_category_is_other: boolean;
  value?: string;
  status?: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateHazardAttributes = ITemplateHazardAttributes;

export type CreateTemplateHazardInput = Omit<
  TemplateHazardAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateHazardInput = Partial<CreateTemplateHazardInput>;
