import {BasicStatus} from '../enums';

export interface ITemplateJobAttributes {
  id: number;
  template_id: number;
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_existing_control: string;
  job_additional_mitigation: string;
  job_close_out_date?: Date;
  job_close_out_responsibility_id?: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export interface ITemplateJobRatingAttributes {
  id: number;
  template_job_id: number;
  parameter_type_id: number;
  rating: string;
  reason?: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export type TemplateJobAttributes = ITemplateJobAttributes;
export type TemplateJobRatingAttributes = ITemplateJobRatingAttributes;

export type CreateTemplateJobInput = Omit<
  TemplateJobAttributes,
  'id' | 'created_at' | 'updated_at'
>;
export type CreateTemplateJobRatingInput = Omit<
  TemplateJobRatingAttributes,
  'id' | 'created_at' | 'updated_at'
>;

export type UpdateTemplateJobInput = Partial<CreateTemplateJobInput>;
export type UpdateTemplateJobRatingInput =
  Partial<CreateTemplateJobRatingInput>;
