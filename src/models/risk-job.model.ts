import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IRiskJobAttributes} from '../types/risk.types';
import CommonModel from './common.model';
import Risk from './risk.model';
import RiskJobInitialRiskRating from './risk-job-initial-risk-rating.model';
import RiskJobResidualRiskRating from './risk-job-residual-risk-rating.model';

export interface RiskJobCreationAttributes
  extends Optional<IRiskJobAttributes, 'id' | 'created_at' | 'updated_at'> {}

const RiskJob = sequelize.define<
  Model<RiskJobCreationAttributes> & IRiskJobAttributes
>(
  'risk_job',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    risk_id: {
      type: DataTypes.INTEGER,
      field: 'risk_id',
      allowNull: false,
      references: {
        model: Risk,
        key: 'id',
      },
    },
    job_step: {
      type: DataTypes.STRING,
      field: 'job_step',
      allowNull: false,
    },
    job_hazard: {
      type: DataTypes.STRING,
      field: 'job_hazard',
      allowNull: false,
    },
    job_nature_of_risk: {
      type: DataTypes.STRING,
      field: 'job_nature_of_risk',
      allowNull: false,
    },
    job_existing_control: {
      type: DataTypes.STRING,
      field: 'job_existing_control',
      allowNull: false,
    },
    job_additional_mitigation: {
      type: DataTypes.STRING(4000),
      field: 'job_additional_mitigation',
      allowNull: true,
    },
    job_close_out_date: {
      type: DataTypes.DATE,
      field: 'job_close_out_date',
      allowNull: true,
    },
    job_close_out_responsibility_id: {
      type: DataTypes.STRING,
      field: 'job_close_out_responsibility_id',
      allowNull: true,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      defaultValue: 1,
      allowNull: false,
    },
    ...CommonModel,
  },
  {
    modelName: 'risk_job',
    tableName: 'risk_job',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupRiskJobAssociations = () => {
  RiskJob.belongsTo(Risk, {
    foreignKey: 'risk_id',
    as: 'risk',
  });

  RiskJob.hasMany(RiskJobInitialRiskRating, {
    foreignKey: 'risk_job_id',
    as: 'risk_job_initial_risk_rating',
    sourceKey: 'id',
  });

  RiskJob.hasMany(RiskJobResidualRiskRating, {
    foreignKey: 'risk_job_id',
    as: 'risk_job_residual_risk_rating',
    sourceKey: 'id',
  });
};

export default RiskJob;
