import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {ITemplateTaskReliabilityAssessmentAttributes} from '../types/template-task-reliability-assessment.types';
import CommonModel from './common.model';
import {BasicStatus} from '../enums';
import Template from './template.model';
import TaskReliabilityAssessment from './task-reliability-assessment.model';

export interface TemplateTaskReliabilityAssessmentCreationAttributes
  extends Optional<
    ITemplateTaskReliabilityAssessmentAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const TemplateTaskReliabilityAssessment = sequelize.define<
  Model<TemplateTaskReliabilityAssessmentCreationAttributes> &
    ITemplateTaskReliabilityAssessmentAttributes
>(
  'template_task_reliability_assessment',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    template_id: {
      type: DataTypes.INTEGER,
      field: 'template_id',
      allowNull: false,
      references: {
        model: Template,
        key: 'id',
      },
    },
    task_reliability_assessment_id: {
      type: DataTypes.INTEGER,
      field: 'task_reliability_assessment_id',
      allowNull: false,
      references: {
        model: TaskReliabilityAssessment,
        key: 'id',
      },
    },
    task_reliability_assessment_answer: {
      type: DataTypes.STRING(3),
      field: 'task_reliability_assessment_answer',
      allowNull: false,
    },
    condition: {
      type: DataTypes.STRING,
      field: 'condition',
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'template_task_reliability_assessment',
    tableName: 'template_task_reliability_assessment',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupTemplateTaskReliabilityAssessmentAssociations = () => {
  // Association with Template
  TemplateTaskReliabilityAssessment.belongsTo(Template, {
    foreignKey: 'template_id',
    as: 'template',
    targetKey: 'id',
  });

  // Association with TaskReliabilityAssessment
  TemplateTaskReliabilityAssessment.belongsTo(TaskReliabilityAssessment, {
    foreignKey: 'task_reliability_assessment_id',
    as: 'task_reliability_assessment',
    targetKey: 'id',
  });
};

export default TemplateTaskReliabilityAssessment;
