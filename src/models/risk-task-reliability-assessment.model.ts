import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IRiskTaskReliabilityAssessmentAttributes} from '../types/risk.types';
import CommonModel from './common.model';
import Risk from './risk.model';
import TaskReliabilityAssessment from './task-reliability-assessment.model';

export interface RiskTaskReliabilityAssessmentCreationAttributes
  extends Optional<
    IRiskTaskReliabilityAssessmentAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const RiskTaskReliabilityAssessment = sequelize.define<
  Model<RiskTaskReliabilityAssessmentCreationAttributes> &
    IRiskTaskReliabilityAssessmentAttributes
>(
  'risk_task_reliability_assessment',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    risk_id: {
      type: DataTypes.INTEGER,
      field: 'risk_id',
      allowNull: false,
      references: {
        model: Risk,
        key: 'id',
      },
    },
    task_reliability_assessment_id: {
      type: DataTypes.INTEGER,
      field: 'task_reliability_assessment_id',
      allowNull: false,
      references: {
        model: TaskReliabilityAssessment,
        key: 'id',
      },
    },
    task_reliability_assessment_answer: {
      type: DataTypes.STRING(3),
      field: 'task_reliability_assessment_answer',
      allowNull: false,
    },
    condition: {
      type: DataTypes.STRING,
      field: 'condition',
      allowNull: true,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      defaultValue: 1,
      allowNull: false,
    },
    ...CommonModel,
  },
  {
    modelName: 'risk_task_reliability_assessment',
    tableName: 'risk_task_reliability_assessment',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupRiskTaskReliabilityAssessmentAssociations = () => {
  RiskTaskReliabilityAssessment.belongsTo(Risk, {
    foreignKey: 'risk_id',
    as: 'risk',
  });

  RiskTaskReliabilityAssessment.belongsTo(TaskReliabilityAssessment, {
    foreignKey: 'task_reliability_assessment_id',
    as: 'task_reliability_assessment',
  });
};

export default RiskTaskReliabilityAssessment;
