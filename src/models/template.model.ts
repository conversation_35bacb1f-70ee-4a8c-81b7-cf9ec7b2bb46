import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {ITemplateAttributes} from '../types/template.types';
import CommonModel from './common.model';
import {RiskTemplateStatus} from '../enums';
import TemplateCategory from './template-category.model';
import TemplateHazard from './template-hazard.model';
import TemplateParameter from './template-parameter.model';
import TemplateJob from './template-job.model';
import TemplateTaskReliabilityAssessment from './template-task-reliability-assessment.model';
import TemplateKeyword from './template-keyword.model';
import Risk from './risk.model';

export interface TemplateCreationAttributes
  extends Optional<ITemplateAttributes, 'id' | 'created_at' | 'updated_at'> {}

const Template = sequelize.define<
  Model<TemplateCreationAttributes> & ITemplateAttributes
>(
  'template',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    task_requiring_ra: {
      type: DataTypes.STRING,
      field: 'task_requiring_ra',
      allowNull: false,
    },
    task_duration: {
      type: DataTypes.STRING,
      field: 'task_duration',
      allowNull: false,
    },
    task_alternative_consideration: {
      type: DataTypes.STRING(4000),
      field: 'task_alternative_consideration',
      allowNull: true,
    },
    task_rejection_reason: {
      type: DataTypes.TEXT,
      field: 'task_rejection_reason',
      allowNull: true,
    },
    worst_case_scenario: {
      type: DataTypes.STRING(4000),
      field: 'worst_case_scenario',
      allowNull: true,
    },
    recovery_measures: {
      type: DataTypes.STRING(4000),
      field: 'recovery_measures',
      allowNull: true,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(RiskTemplateStatus)]},
      defaultValue: RiskTemplateStatus.DRAFT,
    },
    publish_on: {
      type: DataTypes.DATE,
      field: 'publish_on',
    },
    draft_step: {
      type: DataTypes.INTEGER,
      field: 'draft_step',
    },
    ...CommonModel,
  },
  {
    modelName: 'template',
    tableName: 'template',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupTemplateAssociations = () => {
  // Template to TemplateCategory - note the 'template_category' alias matches queries
  Template.hasMany(TemplateCategory, {
    foreignKey: 'template_id',
    as: 'template_category',
    sourceKey: 'id',
  });

  // Template to TemplateHazard
  Template.hasMany(TemplateHazard, {
    foreignKey: 'template_id',
    as: 'template_hazards',
    sourceKey: 'id',
  });

  Template.hasMany(TemplateKeyword, {
    foreignKey: 'template_id',
    as: 'template_keywords',
    sourceKey: 'id',
  });

  // Template to TemplateParameter
  Template.hasMany(TemplateParameter, {
    foreignKey: 'template_id',
    as: 'template_parameter',
    sourceKey: 'id',
  });

  // Template to TemplateJob
  Template.hasMany(TemplateJob, {
    foreignKey: 'template_id',
    as: 'template_job',
    sourceKey: 'id',
  });

  // Template to TemplateTaskReliabilityAssessment
  Template.hasMany(TemplateTaskReliabilityAssessment, {
    foreignKey: 'template_id',
    as: 'template_task_reliability_assessment',
    sourceKey: 'id',
  });

  Template.hasMany(Risk, {
    foreignKey: 'template_id',
    as: 'risks',
    sourceKey: 'id',
  });
};

export default Template;
