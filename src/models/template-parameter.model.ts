import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {ITemplateParameterAttributes} from '../types/template-parameter';
import CommonModel from './common.model';
import {BasicStatus} from '../enums';
import Template from './template.model';
import ParameterType from './parameter-type.model';
import Parameter from './parameter.model';

export interface TemplateParameterCreationAttributes
  extends Optional<
    ITemplateParameterAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const TemplateParameter = sequelize.define<
  Model<TemplateParameterCreationAttributes> & ITemplateParameterAttributes
>(
  'template_parameter',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    template_id: {
      type: DataTypes.INTEGER,
      field: 'template_id',
      allowNull: false,
      references: {
        model: Template,
        key: 'id',
      },
    },
    parameter_type_id: {
      type: DataTypes.INTEGER,
      field: 'parameter_type_id',
    },
    parameter_id: {
      type: DataTypes.INTEGER,
      field: 'parameter_id',
    },
    parameter_is_other: {
      type: DataTypes.BOOLEAN,
      field: 'parameter_is_other',
      defaultValue: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    value: {
      type: DataTypes.STRING,
      field: 'value',
    },
    ...CommonModel,
  },
  {
    modelName: 'template_parameter',
    tableName: 'template_parameter',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupTemplateParameterAssociations = () => {
  // Association with Template
  TemplateParameter.belongsTo(Template, {
    foreignKey: 'template_id',
    as: 'template',
    targetKey: 'id',
  });

  // Association with ParameterType
  TemplateParameter.belongsTo(ParameterType, {
    foreignKey: 'parameter_type_id',
    as: 'parameterType',
    targetKey: 'id',
  });

  // Association with Parameter
  TemplateParameter.belongsTo(Parameter, {
    foreignKey: 'parameter_id',
    as: 'parameter',
    targetKey: 'id',
  });
};

export default TemplateParameter;
