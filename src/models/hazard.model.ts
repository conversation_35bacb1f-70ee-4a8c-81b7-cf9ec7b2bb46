import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import CommonModel from './common.model';
import {MasterDataType, BasicStatus} from '../enums';

interface IHazardAttributes {
  id: number;
  name: string;
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export interface HazardCreationAttributes
  extends Optional<IHazardAttributes, 'id' | 'created_at' | 'updated_at'> {}

const Hazard = sequelize.define<
  Model<HazardCreationAttributes> & IHazardAttributes
>(
  'hazard',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    name: {
      type: DataTypes.STRING,
      field: 'name',
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'hazard',
    tableName: 'hazard',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export default Hazard;
