import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IRiskCategoryAttributes} from '../types/risk.types';
import CommonModel from './common.model';
import Risk from './risk.model';
import Category from './category.model';

export interface RiskCategoryCreationAttributes
  extends Optional<
    IRiskCategoryAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const RiskCategory = sequelize.define<
  Model<RiskCategoryCreationAttributes> & IRiskCategoryAttributes
>(
  'risk_category',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    risk_id: {
      type: DataTypes.INTEGER,
      field: 'risk_id',
      allowNull: false,
      references: {
        model: Risk,
        key: 'id',
      },
    },
    category_id: {
      type: DataTypes.INTEGER,
      field: 'category_id',
      allowNull: false,
      references: {
        model: Category,
        key: 'id',
      },
    },
    category_is_other: {
      type: DataTypes.BOOLEAN,
      field: 'category_is_other',
      defaultValue: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      defaultValue: 1,
      allowNull: false,
    },
    ...CommonModel,
  },
  {
    modelName: 'risk_category',
    tableName: 'risk_category',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupRiskCategoryAssociations = () => {
  RiskCategory.belongsTo(Risk, {
    foreignKey: 'risk_id',
    as: 'risk',
  });

  RiskCategory.belongsTo(Category, {
    foreignKey: 'category_id',
    as: 'category',
  });
};

export default RiskCategory;
