import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IRiskHazardAttributes} from '../types/risk.types';
import CommonModel from './common.model';
import Risk from './risk.model';
import Hazard from './hazard.model';

export interface RiskHazardCreationAttributes
  extends Optional<IRiskHazardAttributes, 'id' | 'created_at' | 'updated_at'> {}

const RiskHazard = sequelize.define<
  Model<RiskHazardCreationAttributes> & IRiskHazardAttributes
>(
  'risk_hazard',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    risk_id: {
      type: DataTypes.INTEGER,
      field: 'risk_id',
      allowNull: false,
      references: {
        model: Risk,
        key: 'id',
      },
    },
    hazard_id: {
      type: DataTypes.INTEGER,
      field: 'hazard_id',
      references: {
        model: Hazard,
        key: 'id',
      },
    },
    hazard_category_is_other: {
      type: DataTypes.BOOLEAN,
      field: 'hazard_category_is_other',
      defaultValue: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      defaultValue: 1,
      allowNull: false,
    },
    ...CommonModel,
  },
  {
    modelName: 'risk_hazard',
    tableName: 'risk_hazard',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupRiskHazardAssociations = () => {
  RiskHazard.belongsTo(Risk, {
    foreignKey: 'risk_id',
    as: 'risk',
  });

  RiskHazard.belongsTo(Hazard, {
    foreignKey: 'hazard_id',
    as: 'hazard_detail',
  });
};

export default RiskHazard;
