import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {ITemplateHazardAttributes} from '../types/template-hazard.types';
import CommonModel from './common.model';
import {MasterDataType, BasicStatus} from '../enums';
import Template from './template.model';
import Hazard from './hazard.model';

export interface TemplateHazardCreationAttributes
  extends Optional<
    ITemplateHazardAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const TemplateHazard = sequelize.define<
  Model<TemplateHazardCreationAttributes> & ITemplateHazardAttributes
>(
  'template_hazard',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    template_id: {
      type: DataTypes.INTEGER,
      field: 'template_id',
      allowNull: false,
      references: {
        model: Template,
        key: 'id',
      },
    },
    hazard_id: {
      type: DataTypes.INTEGER,
      field: 'hazard_id',
    },
    hazard_category_is_other: {
      type: DataTypes.BOOLEAN,
      field: 'hazard_category_is_other',
      defaultValue: false,
    },
    value: {
      type: DataTypes.STRING,
      field: 'value',
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'template_hazard',
    tableName: 'template_hazard',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupTemplateHazardAssociations = () => {
  TemplateHazard.belongsTo(Template, {
    foreignKey: 'template_id',
    as: 'template',
    targetKey: 'id',
  });

  TemplateHazard.belongsTo(Hazard, {
    foreignKey: 'hazard_id',
    as: 'hazard_detail',
    targetKey: 'id',
  });
};

export default TemplateHazard;
