import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IRiskTeamMemberAttributes} from '../types/risk.types';
import CommonModel from './common.model';
import Risk from './risk.model';

export interface RiskTeamMemberCreationAttributes
  extends Optional<
    IRiskTeamMemberAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const RiskTeamMember = sequelize.define<
  Model<RiskTeamMemberCreationAttributes> & IRiskTeamMemberAttributes
>(
  'risk_team_member',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    risk_id: {
      type: DataTypes.INTEGER,
      field: 'risk_id',
      allowNull: false,
      references: {
        model: Risk,
        key: 'id',
      },
    },
    seafarer_id: {
      type: DataTypes.INTEGER,
      field: 'seafarer_id',
      allowNull: true,
    },
    seafarer_person_id: {
      type: DataTypes.INTEGER,
      field: 'seafarer_person_id',
      allowNull: true,
    },
    seafarer_hkid: {
      type: DataTypes.INTEGER,
      field: 'seafarer_hkid',
      allowNull: true,
    },
    seafarer_name: {
      type: DataTypes.STRING,
      field: 'seafarer_name',
      allowNull: false,
    },
    seafarer_rank: {
      type: DataTypes.STRING,
      field: 'seafarer_rank',
      allowNull: false,
    },
    seafarer_rank_id: {
      type: DataTypes.INTEGER,
      field: 'seafarer_rank_id',
      allowNull: true,
    },
    seafarer_rank_sort_order: {
      type: DataTypes.STRING,
      field: 'seafarer_rank_sort_order',
      allowNull: true,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      defaultValue: 1,
      allowNull: false,
    },
    ...CommonModel,
  },
  {
    modelName: 'risk_team_member',
    tableName: 'risk_team_member',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupRiskTeamMemberAssociations = () => {
  RiskTeamMember.belongsTo(Risk, {
    foreignKey: 'risk_id',
    as: 'risk',
  });
};

export default RiskTeamMember;
