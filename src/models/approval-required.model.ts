import {DataTypes, Model} from 'sequelize';
import {sequelize} from '../db/sequelize';
import {ApprovalRequiredAttributes} from '../types/approval-required.types';

class ApprovalRequired
  extends Model<ApprovalRequiredAttributes>
  implements ApprovalRequiredAttributes
{
  public id!: number;
  public name!: string;
  public status!: number;
}

ApprovalRequired.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
  },
  {
    sequelize,
    modelName: 'ApprovalRequired',
    tableName: 'approval_required',
    schema: 'main',
    timestamps: true,
    paranoid: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
  },
);

export default ApprovalRequired;
