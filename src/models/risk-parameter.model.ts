import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IRiskParameterAttributes} from '../types/risk.types';
import CommonModel from './common.model';
import Risk from './risk.model';
import Parameter from './parameter.model';
import ParameterType from './parameter-type.model';

export interface RiskParameterCreationAttributes
  extends Optional<
    IRiskParameterAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const RiskParameter = sequelize.define<
  Model<RiskParameterCreationAttributes> & IRiskParameterAttributes
>(
  'risk_parameter',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    risk_id: {
      type: DataTypes.INTEGER,
      field: 'risk_id',
      allowNull: false,
      references: {
        model: Risk,
        key: 'id',
      },
    },
    parameter_type_id: {
      type: DataTypes.INTEGER,
      field: 'parameter_type_id',
      allowNull: false,
      references: {
        model: ParameterType,
        key: 'id',
      },
    },
    parameter_id: {
      type: DataTypes.INTEGER,
      field: 'parameter_id',
      references: {
        model: Parameter,
        key: 'id',
      },
    },
    parameter_is_other: {
      type: DataTypes.BOOLEAN,
      field: 'parameter_is_other',
      defaultValue: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      defaultValue: 1,
      allowNull: false,
    },
    ...CommonModel,
  },
  {
    modelName: 'risk_parameter',
    tableName: 'risk_parameter',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupRiskParameterAssociations = () => {
  RiskParameter.belongsTo(Risk, {
    foreignKey: 'risk_id',
    as: 'risk',
  });

  RiskParameter.belongsTo(ParameterType, {
    foreignKey: 'parameter_type_id',
    as: 'parameterType',
  });

  RiskParameter.belongsTo(Parameter, {
    foreignKey: 'parameter_id',
    as: 'parameter',
  });
};

export default RiskParameter;
