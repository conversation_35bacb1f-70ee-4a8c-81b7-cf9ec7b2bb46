import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {ITemplateJobRatingAttributes} from '../types/template-job.types';
import CommonModel from './common.model';
import {BasicStatus} from '../enums';
import TemplateJob from './template-job.model';
import ParameterType from './parameter-type.model';

export interface TemplateJobInitialRiskRatingCreationAttributes
  extends Optional<
    ITemplateJobRatingAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const TemplateJobInitialRiskRating = sequelize.define<
  Model<TemplateJobInitialRiskRatingCreationAttributes> &
    ITemplateJobRatingAttributes
>(
  'template_job_initial_risk_rating',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    template_job_id: {
      type: DataTypes.INTEGER,
      field: 'template_job_id',
      allowNull: false,
      references: {
        model: TemplateJob,
        key: 'id',
      },
    },
    parameter_type_id: {
      type: DataTypes.INTEGER,
      field: 'parameter_type_id',
      allowNull: false,
      references: {
        model: ParameterType,
        key: 'id',
      },
    },
    rating: {
      type: DataTypes.STRING(2),
      field: 'rating',
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'template_job_initial_risk_rating',
    tableName: 'template_job_initial_risk_rating',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupTemplateJobInitialRiskRatingAssociations = () => {
  // Association with TemplateJob
  TemplateJobInitialRiskRating.belongsTo(TemplateJob, {
    foreignKey: 'template_job_id',
    as: 'template_job',
    targetKey: 'id',
  });

  // Association with ParameterType
  TemplateJobInitialRiskRating.belongsTo(ParameterType, {
    foreignKey: 'parameter_type_id',
    as: 'parameter_type',
    targetKey: 'id',
  });
};

export default TemplateJobInitialRiskRating;
