import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {ITemplateJobAttributes} from '../types/template-job.types';
import CommonModel from './common.model';
import {BasicStatus} from '../enums';
import Template from './template.model';
import TemplateJobInitialRiskRating from './template-job-initial-risk-rating.model';
import TemplateJobResidualRiskRating from './template-job-residual-risk-rating.model';

export interface TemplateJobCreationAttributes
  extends Optional<
    ITemplateJobAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const TemplateJob = sequelize.define<
  Model<TemplateJobCreationAttributes> & ITemplateJobAttributes
>(
  'template_job',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    template_id: {
      type: DataTypes.INTEGER,
      field: 'template_id',
      allowNull: false,
      references: {
        model: Template,
        key: 'id',
      },
    },
    job_step: {
      type: DataTypes.STRING,
      field: 'job_step',
      allowNull: false,
    },
    job_hazard: {
      type: DataTypes.STRING,
      field: 'job_hazard',
      allowNull: false,
    },
    job_nature_of_risk: {
      type: DataTypes.STRING,
      field: 'job_nature_of_risk',
      allowNull: false,
    },
    job_existing_control: {
      type: DataTypes.STRING(4000),
      field: 'job_existing_control',
      allowNull: false,
    },
    job_additional_mitigation: {
      type: DataTypes.STRING(4000),
      field: 'job_additional_mitigation',
      allowNull: false,
    },
    job_close_out_date: {
      type: DataTypes.DATE,
      field: 'job_close_out_date',
      allowNull: false,
    },
    job_close_out_responsibility_id: {
      type: DataTypes.STRING,
      field: 'job_close_out_responsibility_id',
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'template_job',
    tableName: 'template_job',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupTemplateJobAssociations = () => {
  TemplateJob.belongsTo(Template, {
    foreignKey: 'template_id',
    as: 'template',
    targetKey: 'id',
  });

  TemplateJob.hasMany(TemplateJobInitialRiskRating, {
    foreignKey: 'template_job_id',
    as: 'template_job_initial_risk_rating',
    sourceKey: 'id',
  });

  TemplateJob.hasMany(TemplateJobResidualRiskRating, {
    foreignKey: 'template_job_id',
    as: 'template_job_residual_risk_rating',
    sourceKey: 'id',
  });
};

export default TemplateJob;
