import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {ICategoryAttributes} from '../types/category.types';
import CommonModel from './common.model';
import {MasterDataType, BasicStatus} from '../enums';

export interface CategoryCreationAttributes
  extends Optional<ICategoryAttributes, 'id' | 'created_at' | 'updated_at'> {}

const Category = sequelize.define<
  Model<CategoryCreationAttributes> & ICategoryAttributes
>(
  'category',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    name: {
      type: DataTypes.STRING,
      field: 'name',
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
    },
    type: {
      type: DataTypes.INTEGER,
      field: 'type',
      validate: {isIn: [Object.values(MasterDataType)]},
      allowNull: false,
    },
    ...CommonModel,
  },
  {
    modelName: 'category',
    tableName: 'category',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export default Category;
