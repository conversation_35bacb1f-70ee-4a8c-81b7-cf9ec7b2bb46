import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import CommonModel from './common.model';
import {BasicStatus} from '../enums';

interface ITaskReliabilityAssessmentAttributes {
  id: number;
  name: string;
  options: string[];
  status: BasicStatus;
  created_by?: string;
  created_at?: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export interface TaskReliabilityAssessmentCreationAttributes
  extends Optional<
    ITaskReliabilityAssessmentAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const TaskReliabilityAssessment = sequelize.define<
  Model<TaskReliabilityAssessmentCreationAttributes> &
    ITaskReliabilityAssessmentAttributes
>(
  'task_reliability_assessment',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    name: {
      type: DataTypes.STRING,
      field: 'name',
      allowNull: false,
    },
    options: {
      type: DataTypes.JSON,
      field: 'options',
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'task_reliability_assessment',
    tableName: 'task_reliability_assessment',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export default TaskReliabilityAssessment;
