import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IParameterAttributes} from '../types/parameter.types';
import CommonModel from './common.model';
import {MasterDataType, BasicStatus} from '../enums';
import ParameterType from './parameter-type.model';

export interface ParameterCreationAttributes
  extends Optional<IParameterAttributes, 'id' | 'created_at' | 'updated_at'> {}

const Parameter = sequelize.define<
  Model<ParameterCreationAttributes> & IParameterAttributes
>(
  'parameter',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    parameter_type_id: {
      type: DataTypes.INTEGER,
      field: 'parameter_type_id',
      allowNull: false,
      references: {
        model: ParameterType,
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      field: 'name',
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'parameter',
    tableName: 'parameter',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupParameterAssociations = () => {
  Parameter.belongsTo(ParameterType, {
    foreignKey: 'parameter_type_id',
    as: 'parameter_type',
  });
};

export default Parameter;
