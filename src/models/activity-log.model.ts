import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import CommonModel from './common.model';

export enum EntityType {
  RISK = 1,
  TEMPLATE = 2,
  PARAMETER = 3,
}

export enum ActionType {
  CREATE = 1,
  UPDATE = 2,
  DELETE = 3,
}

export interface IActivityLogAttributes {
  id: number;
  entity_id: number;
  entity_type: EntityType;
  action_type: ActionType;
  parameter: any;
  tenant_id?: number;
  created_by: string;
  created_at: Date;
  updated_by?: string;
  updated_at?: Date;
  deleted_by?: string;
  deleted_at?: Date;
}

export interface ActivityLogCreationAttributes
  extends Optional<
    IActivityLogAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const ActivityLog = sequelize.define<
  Model<ActivityLogCreationAttributes> & IActivityLogAttributes
>(
  'activity_log',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    entity_id: {
      type: DataTypes.INTEGER,
      field: 'entity_id',
      allowNull: false,
    },
    entity_type: {
      type: DataTypes.INTEGER,
      field: 'entity_type',
      allowNull: false,
      validate: {isIn: [Object.values(EntityType)]},
    },
    action_type: {
      type: DataTypes.INTEGER,
      field: 'action_type',
      allowNull: false,
      validate: {isIn: [Object.values(ActionType)]},
    },
    parameter: {
      type: DataTypes.JSON,
      field: 'parameter',
    },
    tenant_id: {
      type: DataTypes.INTEGER,
      field: 'tenant_id',
      allowNull: true,
    },
    ...CommonModel,
  },
  {
    modelName: 'activity_log',
    tableName: 'activity_log',
    schema: 'main',
    timestamps: true,
    underscored: true,
    paranoid: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export default ActivityLog;
