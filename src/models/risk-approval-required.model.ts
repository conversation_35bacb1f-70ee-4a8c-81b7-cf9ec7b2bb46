import {DataTypes, Model} from 'sequelize';
import {sequelize} from '../db/sequelize';
import Risk from './risk.model';
import ApprovalRequired from './approval-required.model';

class RiskApprovalRequired extends Model {
  public id!: number;
  public risk_id!: number;
  public approval_required_id!: number;
  public status!: number;
}

RiskApprovalRequired.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    risk_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Risk,
        key: 'id',
      },
    },
    approval_required_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: ApprovalRequired,
        key: 'id',
      },
    },
    status: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
  },
  {
    sequelize,
    modelName: 'RiskApprovalRequired',
    tableName: 'risk_approval_required',
    schema: 'main',
    timestamps: true,
    paranoid: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    deletedAt: 'deleted_at',
  },
);

export const setupRiskApprovalRequiredAssociations = () => {
  RiskApprovalRequired.belongsTo(Risk, {
    foreignKey: 'risk_id',
    as: 'risk',
  });

  RiskApprovalRequired.belongsTo(ApprovalRequired, {
    foreignKey: 'approval_required_id',
    as: 'approval_required',
  });
};

export default RiskApprovalRequired;
