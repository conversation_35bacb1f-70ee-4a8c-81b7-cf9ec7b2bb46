import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import CommonModel from './common.model';
import {BasicStatus} from '../enums';
import Template from './template.model';
import {ITemplateKeywordAttributes} from 'template-keywords.types';

export interface TemplateKeywordCreationAttributes
  extends Optional<
    ITemplateKeywordAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const TemplateKeyword = sequelize.define<
  Model<TemplateKeywordCreationAttributes> & ITemplateKeywordAttributes
>(
  'template_keyword',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    template_id: {
      type: DataTypes.INTEGER,
      field: 'template_id',
      allowNull: false,
      references: {
        model: Template,
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      field: 'name',
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'template_keyword',
    tableName: 'template_keyword',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupTemplateKeywordAssociations = () => {
  TemplateKeyword.belongsTo(Template, {
    foreignKey: 'template_id',
    as: 'template',
    targetKey: 'id',
  });
};

export default TemplateKeyword;
