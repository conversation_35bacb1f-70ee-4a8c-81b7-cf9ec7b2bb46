import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {ITemplateCategoryAttributes} from '../types/template-category.types';
import CommonModel from './common.model';
import {BasicStatus} from '../enums';
import Template from './template.model';
import Category from './category.model';

export interface TemplateCategoryCreationAttributes
  extends Optional<
    ITemplateCategoryAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const TemplateCategory = sequelize.define<
  Model<TemplateCategoryCreationAttributes> & ITemplateCategoryAttributes
>(
  'template_category',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    template_id: {
      type: DataTypes.INTEGER,
      field: 'template_id',
      allowNull: false,
      references: {
        model: Template,
        key: 'id',
      },
    },
    category_id: {
      type: DataTypes.INTEGER,
      field: 'category_id',
      allowNull: false,
      references: {
        model: Category,
        key: 'id',
      },
    },
    category_is_other: {
      type: DataTypes.BOOLEAN,
      field: 'category_is_other',
      defaultValue: false,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'template_category',
    tableName: 'template_category',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupTemplateCategoryAssociations = () => {
  TemplateCategory.belongsTo(Template, {
    foreignKey: 'template_id',
    as: 'template',
  });

  TemplateCategory.belongsTo(Category, {
    foreignKey: 'category_id',
    as: 'category',
  });
};

export default TemplateCategory;
