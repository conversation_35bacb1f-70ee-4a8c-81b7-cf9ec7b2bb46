import {DataTypes} from 'sequelize';

const CommonModel = {
  created_by: {
    type: DataTypes.STRING,
    field: 'created_by',
    require: true,
    notNull: true,
  },
  updated_by: {
    type: DataTypes.STRING,
    field: 'updated_by',
  },
  deleted_at: {
    type: DataTypes.DATE,
    field: 'deleted_at',
  },
  deleted_by: {
    type: DataTypes.STRING,
    field: 'deleted_by',
  },
};

export default CommonModel;
