import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IRiskAttributes} from '../types/risk.types';
import CommonModel from './common.model';
import {RiskTemplateStatus} from '../enums';
import RiskTeamMember from './risk-team-member.model';
import RiskCategory from './risk-category.model';
import RiskHazard from './risk-hazard.model';
import RiskParameter from './risk-parameter.model';
import RiskJob from './risk-job.model';
import RiskTaskReliabilityAssessment from './risk-task-reliability-assessment.model';
import RiskApprover from './risk-approver.model';
import RiskApprovalRequired from './risk-approval-required.model';
import Template from './template.model';

export interface RiskCreationAttributes
  extends Optional<IRiskAttributes, 'id' | 'created_at' | 'updated_at'> {}

const Risk = sequelize.define<Model<RiskCreationAttributes> & IRiskAttributes>(
  'risk',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    template_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'template_id',
    },
    task_requiring_ra: {
      type: DataTypes.STRING,
      field: 'task_requiring_ra',
      allowNull: false,
    },
    assessor: {
      type: DataTypes.INTEGER,
      field: 'assessor',
      allowNull: false,
    },
    vessel_ownership_id: {
      type: DataTypes.INTEGER,
      field: 'vessel_ownership_id',
      allowNull: true,
    },
    vessel_id: {
      type: DataTypes.INTEGER,
      field: 'vessel_id',
      allowNull: true,
    },
    vessel_code: {
      type: DataTypes.INTEGER,
      field: 'vessel_code',
      allowNull: true,
    },
    vessel_name: {
      type: DataTypes.STRING,
      field: 'vessel_name',
      allowNull: true,
    },
    vessel_tech_group: {
      type: DataTypes.STRING,
      field: 'vessel_tech_group',
      allowNull: true,
    },
    vessel_category: {
      type: DataTypes.STRING,
      field: 'vessel_category',
      allowNull: true,
    },
    office_id: {
      type: DataTypes.INTEGER,
      field: 'office_id',
      allowNull: true,
    },
    office_name: {
      type: DataTypes.STRING,
      field: 'office_name',
      allowNull: true,
    },
    date_risk_assessment: {
      type: DataTypes.DATE,
      field: 'date_risk_assessment',
      allowNull: true,
    },
    task_duration: {
      type: DataTypes.STRING,
      field: 'task_duration',
      allowNull: false,
    },
    ra_level: {
      type: DataTypes.INTEGER,
      field: 'ra_level',
    },
    task_alternative_consideration: {
      type: DataTypes.STRING(4000),
      field: 'task_alternative_consideration',
      allowNull: true,
    },
    task_rejection_reason: {
      type: DataTypes.TEXT,
      field: 'task_rejection_reason',
      allowNull: true,
    },
    worst_case_scenario: {
      type: DataTypes.STRING(4000),
      field: 'worst_case_scenario',
      allowNull: true,
    },
    recovery_measures: {
      type: DataTypes.STRING(4000),
      field: 'recovery_measures',
      allowNull: true,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(RiskTemplateStatus)]},
      defaultValue: RiskTemplateStatus.DRAFT,
    },
    publish_on: {
      type: DataTypes.DATE,
      field: 'publish_on',
    },
    approval_date: {
      type: DataTypes.DATE,
      field: 'approval_date',
    },
    draft_step: {
      type: DataTypes.INTEGER,
      field: 'draft_step',
    },
    ...CommonModel,
  },
  {
    modelName: 'risk',
    tableName: 'risk',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupRiskAssociations = () => {
  // Risk to RiskTeamMember
  Risk.hasMany(RiskTeamMember, {
    foreignKey: 'risk_id',
    as: 'risk_team_member',
    sourceKey: 'id',
  });

  // Risk to RiskCategory
  Risk.hasMany(RiskCategory, {
    foreignKey: 'risk_id',
    as: 'risk_category',
    sourceKey: 'id',
  });

  // Risk to RiskHazard
  Risk.hasMany(RiskHazard, {
    foreignKey: 'risk_id',
    as: 'risk_hazards',
    sourceKey: 'id',
  });

  // Risk to RiskParameter
  Risk.hasMany(RiskParameter, {
    foreignKey: 'risk_id',
    as: 'risk_parameter',
    sourceKey: 'id',
  });

  // Risk to RiskJob
  Risk.hasMany(RiskJob, {
    foreignKey: 'risk_id',
    as: 'risk_job',
    sourceKey: 'id',
  });

  // Risk to RiskTaskReliabilityAssessment
  Risk.hasMany(RiskTaskReliabilityAssessment, {
    foreignKey: 'risk_id',
    as: 'risk_task_reliability_assessment',
    sourceKey: 'id',
  });

  // Risk to RiskApprover
  Risk.hasMany(RiskApprover, {
    foreignKey: 'risk_id',
    as: 'risk_approver',
    sourceKey: 'id',
  });

  // Risk to RiskApprovalRequired
  Risk.hasMany(RiskApprovalRequired, {
    foreignKey: 'risk_id',
    as: 'risk_approval_required',
    sourceKey: 'id',
  });

  // Risk to Template
  Risk.belongsTo(Template, {
    foreignKey: 'template_id',
    as: 'template',
  });
};

export default Risk;
