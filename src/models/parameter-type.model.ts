import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IParameterTypeAttributes} from '../types/parameter-type.types';
import CommonModel from './common.model';
import {BasicStatus} from '../enums';

import Parameter from './parameter.model';

export interface ParameterTypeCreationAttributes
  extends Optional<
    IParameterTypeAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const ParameterType = sequelize.define<
  Model<ParameterTypeCreationAttributes> & IParameterTypeAttributes
>(
  'parameter_type',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    name: {
      type: DataTypes.STRING,
      field: 'name',
      allowNull: false,
    },
    is_required_for_risk_rating: {
      type: DataTypes.BOOLEAN,
      field: 'is_required_for_risk_rating',
      allowNull: false,
      defaultValue: true,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      validate: {isIn: [Object.values(BasicStatus)]},
      defaultValue: BasicStatus.ACTIVE,
    },
    ...CommonModel,
  },
  {
    modelName: 'parameter_type',
    tableName: 'parameter_type',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupParameterTypeAssociations = () => {
  ParameterType.hasMany(Parameter, {
    foreignKey: 'parameter_type_id',
    as: 'parameters',
    sourceKey: 'id',
  });
};

export default ParameterType;
