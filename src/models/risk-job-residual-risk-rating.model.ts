import {DataTypes, Model, Optional} from 'sequelize';
import sequelize from '../db/sequelize';
import {IRiskJobResidualRatingAttributes} from '../types/risk.types';
import CommonModel from './common.model';
import RiskJob from './risk-job.model';
import ParameterType from './parameter-type.model';

export interface RiskJobResidualRatingCreationAttributes
  extends Optional<
    IRiskJobResidualRatingAttributes,
    'id' | 'created_at' | 'updated_at'
  > {}

const RiskJobResidualRiskRating = sequelize.define<
  Model<RiskJobResidualRatingCreationAttributes> &
    IRiskJobResidualRatingAttributes
>(
  'risk_job_residual_risk_rating',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      field: 'id',
    },
    risk_job_id: {
      type: DataTypes.INTEGER,
      field: 'risk_job_id',
      allowNull: false,
      references: {
        model: RiskJob,
        key: 'id',
      },
    },
    parameter_type_id: {
      type: DataTypes.INTEGER,
      field: 'parameter_type_id',
      allowNull: false,
      references: {
        model: ParameterType,
        key: 'id',
      },
    },
    rating: {
      type: DataTypes.STRING(2),
      field: 'rating',
      allowNull: false,
    },
    reason: {
      type: DataTypes.STRING,
      field: 'reason',
      allowNull: true,
    },
    status: {
      type: DataTypes.INTEGER,
      field: 'status',
      defaultValue: 1,
      allowNull: false,
    },
    ...CommonModel,
  },
  {
    modelName: 'risk_job_residual_risk_rating',
    tableName: 'risk_job_residual_risk_rating',
    schema: 'main',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  },
);

export const setupRiskJobResidualRiskRatingAssociations = () => {
  RiskJobResidualRiskRating.belongsTo(RiskJob, {
    foreignKey: 'risk_job_id',
    as: 'risk_job',
  });

  RiskJobResidualRiskRating.belongsTo(ParameterType, {
    foreignKey: 'parameter_type_id',
    as: 'parameter_type',
  });
};

export default RiskJobResidualRiskRating;
