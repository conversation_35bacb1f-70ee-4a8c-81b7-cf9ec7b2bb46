import {APIGatewayProxyEventPathParameters} from 'aws-lambda';

export interface PathParameters {
  [key: string]: string | number;
}

export function validatePathParameters(
  params: APIGatewayProxyEventPathParameters | null,
): boolean {
  if (!params || Object.keys(params).length === 0) return false;

  // Convert path parameters to the expected format and validate
  const validatedParams: PathParameters = {};
  for (const [key, value] of Object.entries(params)) {
    if (value === undefined || value === null) return false;
    validatedParams[key] = value;

    // Try to convert to number if possible, otherwise keep as string
    if (value !== '') {
      const numValue = Number(value);
      if (!isNaN(numValue)) {
        validatedParams[key] = numValue;
      }
    }
  }

  return true;
}
