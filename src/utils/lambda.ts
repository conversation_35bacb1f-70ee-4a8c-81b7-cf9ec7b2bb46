import {v4 as uuidv4} from 'uuid';
import Busboy from 'busboy';
import loggerConfig from '../config/logger.config';
import {initDb, closeDb} from '../db/db-client';
import {validatePathParameters} from './validatePathParameters';
import {initLogger, closeLogger, logger} from './logger';
import {
  LambdaConfig,
  LambdaEvent,
  LambdaContext,
  LambdaData,
  LambdaHandler,
  LambdaError,
  MultiFormData,
  FileMetadata,
  HttpApiRequestContext,
} from '../types/lambda';
import {CustomHttpError} from '../error';
import {StatusCodes} from '../enums';

export const DATA_TYPE_FILE = 'file';
export const DATA_TYPE_JSON = 'json';

export function getOriginUrl() {
  if (['dev2', 'qa2'].includes(process.env.NODE_ENV ?? '')) {
    return `https://paris2-${process.env.NODE_ENV}.fleetship.com`;
  } else if (process.env.NODE_ENV === 'uat') {
    return `https://paris2-uat2.fleetship.com`;
  } else if (process.env.NODE_ENV === 'prod') {
    return `https://paris2.fleetship.com`;
  } else {
    return 'https://default.fleetship.com';
  }
}

const baseHeaders = {
  'Access-Control-Allow-Origin': getOriginUrl(),
  'Access-Control-Allow-Credentials': true,
  'Access-Control-Allow-Headers': '*',
  'Strict-Transport-Security': 'max-age=63072000; includeSubDomains',
};

const {logstashHost, logLevel} = loggerConfig();

class Lambda {
  private config: LambdaConfig;
  private initial_handler: LambdaHandler;

  constructor(initial_handler: LambdaHandler, config: LambdaConfig = {}) {
    this.config = config;
    this.initial_handler = initial_handler;
  }

  private getMethodFromRequestContext(
    requestContext: HttpApiRequestContext | Record<string, unknown>,
  ): string {
    if (
      typeof requestContext === 'object' &&
      requestContext !== null &&
      'http' in requestContext &&
      typeof (requestContext as {http: {method?: string}}).http === 'object'
    ) {
      return (requestContext as {http: {method?: string}}).http.method ?? '';
    }
    if (
      typeof requestContext === 'object' &&
      requestContext !== null &&
      'httpMethod' in requestContext
    ) {
      return (requestContext as {httpMethod?: string}).httpMethod ?? '';
    }
    return '';
  }

  getMissingRoles = async (
    lambdaRoles: string[] | ((data: LambdaData) => Promise<string[]>),
    data: LambdaData,
  ): Promise<string[]> => {
    let requiredRoles = lambdaRoles;
    if (typeof lambdaRoles === 'function') {
      requiredRoles = await lambdaRoles(data);
    }
    return (requiredRoles as string[]).filter(
      role => !(data?.userRoles ?? []).includes(role),
    );
  };

  parseMultiFormData = (event: LambdaEvent): Promise<MultiFormData> =>
    new Promise((resolve, reject) => {
      const contentType =
        event.headers?.['content-type'] ??
        event.headers?.['Content-Type'] ??
        '';
      if (!contentType.includes('multipart/form-data')) {
        reject(
          new Error(
            'Invalid Content-Type header: multipart/form-data required',
          ),
        );
        return;
      }
      const busboy = Busboy({headers: {'content-type': contentType}});
      const result: MultiFormData = {
        files: [],
        formFields: {},
      };

      busboy.on('file', (fieldname: string, file: any, fileMetaData: any) => {
        const uploadFile: FileMetadata = {
          filename: fileMetaData.filename,
          mimeType: fileMetaData.mimeType,
          encoding: fileMetaData.encoding,
          fieldname,
        };

        file.on('data', (data: Buffer) => {
          uploadFile.content = data;
        });

        file.on('end', () => {
          result.files.push(uploadFile);
        });
      });

      busboy.on('field', (fieldname: string, value: string) => {
        result.formFields[fieldname] = value;
      });

      busboy.on('error', (error: Error) => {
        reject(error);
      });

      busboy.on('finish', () => {
        resolve(result);
      });

      if (event.body) {
        const chunk = event.isBase64Encoded
          ? Buffer.from(event.body, 'base64')
          : Buffer.from(event.body);

        busboy.write(chunk);
      }
      busboy.end();
    });

  parseJson(event: LambdaEvent): Record<string, any> {
    if (!event.body) return {};
    let decodedBody = event.body;
    const {isBase64Encoded} = event;
    try {
      if (isBase64Encoded) {
        const buff = Buffer.from(decodedBody, 'base64');
        decodedBody = buff.toString('utf8');
      }

      return JSON.parse(decodedBody);
    } catch (error) {
      throw new CustomHttpError(
        StatusCodes.BAD_REQUEST,
        'The request body contains invalid JSON. Please check the formatting and try again.',
      );
    }
  }

  validatePathParametersValue(
    pathParameters: {[key: string]: string} | undefined,
  ) {
    if (pathParameters && !validatePathParameters(pathParameters)) {
      return {
        statusCode: 400,
        headers: baseHeaders,
        body: JSON.stringify({error: 'Invalid or missing parameter'}),
      };
    }
  }

  private getLogstashFields(
    context: LambdaContext,
    requestContext: any,
    requestId: string,
    method: string,
  ): Record<string, any> {
    let fields: Record<string, any> = {
      type: 'LAMBDA_REQUEST',
      method,
      function: context.functionName,
      request_id: requestId,
    };
    if (requestContext?.authorizer) {
      const {user_id, user_name, ship_party_id, scope} =
        requestContext.authorizer;
      fields = {...fields, user_id, user_name, ship_party_id, scope};
    }
    return fields;
  }

  private async parseBody(
    event: LambdaEvent,
    dataType: string | undefined,
    body: string | null | undefined,
  ): Promise<{
    jsonBody: Record<string, any>;
    files: FileMetadata[];
    formFields: Record<string, string>;
  }> {
    if (!body) return {jsonBody: {}, files: [], formFields: {}};
    try {
      if (dataType === DATA_TYPE_JSON || !dataType) {
        try {
          return {jsonBody: this.parseJson(event), files: [], formFields: {}};
        } catch (error) {
          if (
            error instanceof CustomHttpError &&
            error.statusCode === StatusCodes.BAD_REQUEST
          ) {
            throw error;
          }
          throw new CustomHttpError(
            StatusCodes.BAD_REQUEST,
            'Failed to parse JSON body. Please check the formatting and try again.',
          );
        }
      }
      if (dataType === DATA_TYPE_FILE) {
        const parsedResult = await this.parseMultiFormData(event);
        return {
          jsonBody: {},
          files: parsedResult.files,
          formFields: parsedResult.formFields,
        };
      }
      return {jsonBody: this.parseJson(event), files: [], formFields: {}};
    } catch (error) {
      logger.error({
        group: 'unknown',
        name: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  handler = async (
    event: LambdaEvent,
    context: LambdaContext,
  ): Promise<{
    statusCode: number;
    headers: Record<string, string | boolean>;
    body: string;
  }> => {
    const {
      db = [],
      roles: lambdaRoles = [],
      validateAccess,
      dataType,
    } = this.config;
    const {
      body,
      pathParameters,
      requestContext,
      queryStringParameters,
      multiValueQueryStringParameters,
    } = event;

    const method = requestContext
      ? this.getMethodFromRequestContext(
          requestContext as HttpApiRequestContext,
        )
      : '';

    const pathParamsValidation =
      this.validatePathParametersValue(pathParameters);
    if (pathParamsValidation) {
      return pathParamsValidation;
    }

    const requestId = uuidv4();

    // --- Helper: Setup logger fields ---
    const logstashFields = this.getLogstashFields(
      context,
      requestContext,
      requestId,
      method,
    );

    // --- Helper: Parse body ---
    const {jsonBody, files, formFields} = await this.parseBody(
      event,
      dataType,
      body,
    );

    // --- Helper: Build LambdaData ---
    const buildLambdaData = (
      user: any,
      jsonBody: Record<string, any>,
      files: FileMetadata[],
      formFields: Record<string, string>,
    ): LambdaData => {
      let data: LambdaData = {
        method,
        jsonBody,
        body,
        pathParameters,
        queryStringParameters,
        multiValueQueryStringParameters,
        requestId,
        formFields,
        files,
        user: {user_id: user?.user_id},
      };
      const userRoles: string[] = user?.roles?.split(',') || [];
      data = {
        ...data,
        user: {...user},
        userRoles,
      };
      return data;
    };

    try {
      // Initialize database connection if needed
      if (db && db.length > 0) {
        try {
          await initDb();
        } catch (dbError) {
          // Log the database initialization error but don't fail immediately
          logger.error({
            group: 'database',
            name: 'init_error',
            message: `Database initialization failed: ${dbError instanceof Error ? dbError.message : 'Unknown error'}`,
          });

          // For connection errors, we'll let the individual operations handle retries
          // rather than failing the entire Lambda invocation
          if (
            dbError instanceof Error &&
            (dbError.message.includes('Connection terminated unexpectedly') ||
              dbError.message.includes('connection was closed'))
          ) {
            logger.warn({
              group: 'database',
              name: 'connection_recovery',
              message:
                'Database connection issue detected, operations will retry as needed',
            });
          } else {
            // For other database errors, fail fast
            throw dbError;
          }
        }
      }

      await initLogger({
        logstashHost,
        logLevel,
        logstashFields: logstashFields,
      });

      const user = requestContext?.authorizer;
      let data = buildLambdaData(user, jsonBody, files, formFields);
      if (lambdaRoles && lambdaRoles.length > 0) {
        const missingRoles = await this.getMissingRoles(lambdaRoles, data);
        if (missingRoles.length > 0) {
          return {
            statusCode: StatusCodes.FORBIDDEN,
            headers: {
              ...baseHeaders,
              'content-type': 'text/plain',
            },
            body: `Access denied. Missing roles: ${missingRoles.join(', ')}`,
          };
        }
      }
      logger.debug({
        group: 'request',
        name: 'payload',
        message: data,
      });

      if (validateAccess) {
        await validateAccess(data);
      }

      const {
        statusCode = 200,
        response = {},
        headers = {'Content-Type': 'application/json'},
      } = await this.initial_handler(data, context);

      // Close database connection before sending response
      if (db && db.length > 0) {
        await closeDb();
      }

      return {
        statusCode,
        headers: {
          ...baseHeaders,
          ...headers,
        },
        body: JSON.stringify(response, null, 2),
      };
    } catch (error) {
      const err = error as LambdaError;
      logger.error({
        group: 'unknown',
        name: 'error',
        message: err.message || 'Unknown error',
      });

      console.log({db}, 'Db ');
      // No need to force close connections in Lambda - AWS handles cleanup

      return {
        statusCode: err.statusCode ?? 500,
        headers: {
          ...baseHeaders,
          'content-type': 'text/plain',
        },
        body: err.message || 'internal server error',
      };
    } finally {
      await closeLogger();
    }
  };
}

const createLambda = (handle: LambdaHandler, config?: LambdaConfig) => {
  const {handler} = new Lambda(handle, config);
  return handler;
};

export {Lambda, createLambda};
