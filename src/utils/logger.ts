interface LoggerConfig {
  logstashHost?: string;
  logLevel?: string;
  logstashFields?: Record<string, any>;
}

interface LogEntry {
  group: string;
  name: string;
  message: any;
}

class Logger {
  private static instance: Logger;
  private config: LoggerConfig = {};

  private constructor() {}

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public configure(config: LoggerConfig) {
    this.config = config;
  }

  private log(level: string, entry: LogEntry) {
    const timestamp = new Date().toISOString();
    const logData = {
      timestamp,
      level,
      ...this.config.logstashFields,
      ...entry,
    };

    // For now, just use console.log. In production, you might want to use a proper logging service
    console.log(JSON.stringify(logData));
  }

  public debug(entry: LogEntry) {
    if (this.config.logLevel === 'debug') {
      this.log('debug', entry);
    }
  }

  public info(entry: LogEntry) {
    this.log('info', entry);
  }

  public warn(entry: LogEntry) {
    this.log('warn', entry);
  }

  public error(entry: LogEntry) {
    this.log('error', entry);
  }
}

export const logger = Logger.getInstance();

export const initLogger = async (config: LoggerConfig): Promise<void> => {
  logger.configure(config);
};

export const closeLogger = async (): Promise<void> => {
  // Cleanup if needed
  return Promise.resolve();
};
