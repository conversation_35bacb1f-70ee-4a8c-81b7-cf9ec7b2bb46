import {StatusCodes} from '../enums';

export const handleError = (error: any) => {
  console.error('Error:', error);

  if (error.name === 'SequelizeValidationError') {
    return {
      statusCode: StatusCodes.BAD_REQUEST,
      response: {
        message: error.message,
        errors: error.errors.map((err: any) => ({
          field: err.path,
          message: err.message,
        })),
      },
    };
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return {
      statusCode: StatusCodes.BAD_REQUEST,
      response: {
        message: `Invalid reference provided for ${String(error.table).replace(
          /_/g,
          ' ',
        )}`,
      },
    };
  }

  // Handle database connection pool exhaustion
  if (
    error.name === 'SequelizeConnectionError' ||
    error.name === 'ConnectionError' ||
    (error.message &&
      (error.message.includes('remaining connection slots are reserved') ||
        error.message.includes('too many connections') ||
        error.message.includes('connection limit exceeded') ||
        error.message.includes('Connection terminated unexpectedly') ||
        error.message.includes('connection was closed')))
  ) {
    console.error('Database connection error:', error);

    // Specific handling for connection pool exhaustion
    if (
      error.message &&
      error.message.includes('remaining connection slots are reserved')
    ) {
      return {
        statusCode: 503, // Service Unavailable
        response: {
          message:
            'Service temporarily overloaded. Please try again in a few moments.',
        },
      };
    }

    return {
      statusCode: 503, // Service Unavailable
      response: {
        message: 'Database service temporarily unavailable. Please try again.',
      },
    };
  }

  return {
    statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
    response: {
      message: 'Internal server error',
    },
  };
};

export const handleValidationError = (message: string) => ({
  statusCode: StatusCodes.BAD_REQUEST,
  response: {
    message,
  },
});

export const handleNotFoundError = (resource: string) => ({
  statusCode: StatusCodes.NOT_FOUND,
  response: {
    message: `${resource} not found`,
  },
});

export class CustomHttpError extends Error {
  statusCode: number;
  response: {message: string};
  constructor(statusCode: number, message: string) {
    super(message);
    this.statusCode = statusCode;
    this.response = {message};
    Object.setPrototypeOf(this, new.target.prototype);
  }
}
