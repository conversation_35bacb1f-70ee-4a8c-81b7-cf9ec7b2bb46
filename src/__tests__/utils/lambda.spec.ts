// Import necessary testing libraries
import {expect} from 'chai';
// Import necessary modules and functions
import {Lambda, createLamb<PERSON>, DATA_TYPE_JSON} from '../../utils/lambda';
import {LambdaHandler, LambdaEvent, LambdaContext} from '../../types/lambda';
import sinon from 'sinon';
import {getOriginUrl} from '../../utils/lambda';
import {CustomHttpError} from '../../error';
import {StatusCodes} from '../../enums';

describe('Lambda Class', () => {
  let lambda: Lambda;

  beforeEach(() => {
    const mockHandler: LambdaHandler = async (data, context) => ({
      statusCode: 200,
      response: {success: true},
    });
    lambda = new Lambda(mockHandler, {dataType: DATA_TYPE_JSON});
  });

  it('should parse JSON correctly', () => {
    const event: LambdaEvent = {
      body: JSON.stringify({key: 'value'}),
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const result = lambda.parseJson(event);
    expect(result).to.deep.equal({key: 'value'});
  });

  it('should handle missing roles correctly', async () => {
    const lambdaRoles = ['admin', 'editor'];
    const data = {
      userRoles: ['viewer'],
      requestId: 'mockRequestId',
      jsonBody: {},
      body: '',
      pathParameters: {},
      queryStringParameters: {},
      multiValueQueryStringParameters: {},
      formFields: {},
      files: [],
      user: {},
    };
    const missingRoles = await lambda.getMissingRoles(lambdaRoles, data as any);
    expect(missingRoles).to.deep.equal(['admin', 'editor']);
  });

  it('should resolve lambdaRoles when it is a function', async () => {
    const lambda = new Lambda(async () => ({statusCode: 200, response: {}}));
    const lambdaRolesFn = async (data: any) => ['admin', 'editor'];
    const data = {userRoles: ['admin']};

    const missingRoles = await lambda.getMissingRoles(
      lambdaRolesFn,
      data as any,
    );

    expect(missingRoles).to.deep.equal(['editor']);
  });

  it('should throw CustomHttpError with BAD_REQUEST for invalid JSON', () => {
    const lambda = new Lambda(async () => ({statusCode: 200, response: {}}));
    const event: LambdaEvent = {
      body: '{invalidJson:}', // invalid JSON
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    try {
      lambda.parseJson(event);
      expect.fail('Expected CustomHttpError was not thrown');
    } catch (err: any) {
      expect(err).to.be.instanceOf(require('../../error').CustomHttpError);
      expect(err.statusCode).to.equal(400);
      expect(err.message).to.include('The request body contains invalid JSON');
    }
  });

  it('should handle multi-form data correctly', async () => {
    const event: LambdaEvent = {
      headers: {
        'content-type':
          'multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW',
      },
      body: '------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name="field1"\r\n\r\nvalue1\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name="file"; filename="test.txt"\r\nContent-Type: text/plain\r\n\r\nfile content\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--',
      isBase64Encoded: false,
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const result = await lambda.parseMultiFormData(event);
    expect(result.formFields).to.deep.equal({field1: 'value1'});
    expect(result.files).to.have.lengthOf(1);
    expect(result.files[0]).to.include({
      filename: 'test.txt',
      mimeType: 'text/plain',
    });
  });

  it('should execute handler correctly', async () => {
    const event: LambdaEvent = {
      body: JSON.stringify({key: 'value'}),
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const context: LambdaContext = {
      functionName: 'testFunction',
      callbackWaitsForEmptyEventLoop: false,
      functionVersion: '',
      invokedFunctionArn: '',
      memoryLimitInMB: '',
      awsRequestId: '',
      logGroupName: '',
      logStreamName: '',
      identity: undefined,
      clientContext: undefined,
      getRemainingTimeInMillis: () => 1000,
      done: () => {},
      fail: () => {},
      succeed: () => {},
    };
    const response = await lambda.handler(event, context);
    expect(response.statusCode).to.equal(200);
    expect(JSON.parse(response.body)).to.deep.equal({success: true});
  });
});

describe('Lambda internal and edge cases', () => {
  let lambda: Lambda;

  beforeEach(() => {
    const mockHandler: LambdaHandler = async (data, context) => ({
      statusCode: 200,
      response: {success: true},
    });
    lambda = new Lambda(mockHandler, {dataType: DATA_TYPE_JSON});
  });

  it('should get method from requestContext with http', () => {
    // @ts-ignore
    const method = lambda['getMethodFromRequestContext']({
      http: {method: 'POST'},
    });
    expect(method).to.equal('POST');
  });

  it('should get method from requestContext with httpMethod', () => {
    // @ts-ignore
    const method = lambda['getMethodFromRequestContext']({httpMethod: 'PUT'});
    expect(method).to.equal('PUT');
  });

  it('should return empty string if requestContext has no http or httpMethod', () => {
    // @ts-ignore
    const method = lambda['getMethodFromRequestContext']({});
    expect(method).to.equal('');
  });

  it('should reject parseMultiFormData if content-type is missing', async () => {
    const event: LambdaEvent = {
      headers: {},
      body: '',
      isBase64Encoded: false,
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    try {
      await lambda.parseMultiFormData(event);
      // If no error is thrown, fail the test
      expect.fail('Expected error was not thrown');
    } catch (err: any) {
      expect(err.message).to.include('Invalid Content-Type header');
    }
  });

  it('should parseJson with base64 encoded body', () => {
    const obj = {foo: 'bar'};
    const event: LambdaEvent = {
      body: Buffer.from(JSON.stringify(obj)).toString('base64'),
      isBase64Encoded: true,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const result = lambda.parseJson(event);
    expect(result).to.deep.equal(obj);
  });

  it('should parseJson return empty object if body is missing', () => {
    const event: LambdaEvent = {
      body: undefined,
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const result = lambda.parseJson(event);
    expect(result).to.deep.equal({});
  });

  it('should return 400 if pathParameters are invalid', async () => {
    const event: LambdaEvent = {
      body: JSON.stringify({}),
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
      pathParameters: {invalid: 'true'},
    };
    // Force validatePathParameters to return false
    const orig =
      require('../../utils/validatePathParameters').validatePathParameters;
    require('../../utils/validatePathParameters').validatePathParameters = () =>
      false;
    const response = await lambda.handler(event, {} as LambdaContext);
    expect(response.statusCode).to.equal(400);
    require('../../utils/validatePathParameters').validatePathParameters = orig;
  });

  it('should handle error thrown by handler', async () => {
    const errorLambda = new Lambda(
      async () => {
        throw Object.assign(new Error('fail'), {statusCode: 418});
      },
      {dataType: DATA_TYPE_JSON},
    );
    const event: LambdaEvent = {
      body: JSON.stringify({}),
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const context = {} as LambdaContext;
    const response = await errorLambda.handler(event, context);
    expect(response.statusCode).to.equal(418);
    expect(response.body).to.include('fail');
  });
});

describe('createLambda with config', () => {
  it('should create a Lambda handler with config', () => {
    const mockHandler: LambdaHandler = async (data, context) => ({
      statusCode: 201,
      response: {ok: true},
    });
    const handler = createLambda(mockHandler, {dataType: DATA_TYPE_JSON});
    expect(typeof handler).to.equal('function');
  });
});

describe('Lambda handler switch-case coverage', () => {
  it('should use default case in handler switch when dataType is unknown', async () => {
    const mockHandler: LambdaHandler = async (data, context) => ({
      statusCode: 200,
      response: {received: data.jsonBody},
    });
    const lambda = new Lambda(mockHandler, {dataType: 'unknown-type' as any});
    const event: LambdaEvent = {
      body: JSON.stringify({foo: 'bar'}),
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const context = {} as LambdaContext;
    const response = await lambda.handler(event, context);
    expect(response.statusCode).to.equal(200);
    expect(JSON.parse(response.body)).to.deep.equal({received: {foo: 'bar'}});
  });

  it('should use default case in handler switch when dataType is undefined', async () => {
    const mockHandler: LambdaHandler = async (data, context) => ({
      statusCode: 200,
      response: {received: data.jsonBody},
    });
    const lambda = new Lambda(mockHandler, {}); // no dataType
    const event: LambdaEvent = {
      body: JSON.stringify({baz: 'qux'}),
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const context = {} as LambdaContext;
    const response = await lambda.handler(event, context);
    expect(response.statusCode).to.equal(200);
    expect(JSON.parse(response.body)).to.deep.equal({received: {baz: 'qux'}});
  });
});

describe('Lambda handler error catch block', () => {
  it('should log error and return 500 with message if handler throws generic error', async () => {
    const errorLambda = new Lambda(
      async () => {
        throw new Error('Something went wrong');
      },
      {dataType: DATA_TYPE_JSON},
    );
    const event: LambdaEvent = {
      body: JSON.stringify({}),
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const context = {} as LambdaContext;
    const response = await errorLambda.handler(event, context);
    expect(response.statusCode).to.equal(500);
    expect(response.body).to.include('Something went wrong');
    expect(response.headers['content-type']).to.equal('text/plain');
  });
});

describe('Lambda utility and handler edge cases', () => {
  let lambda: Lambda;

  beforeEach(() => {
    lambda = new Lambda(async () => ({statusCode: 200, response: {}}));
    sinon.restore();
  });

  // it('getMissingRoles should resolve roles from function', async () => {
  //   const lambdaRolesFn = async (data: any) => ['role1', 'role2'];
  //   const data = {userRoles: ['role1']};
  //   const missing = await lambda.getMissingRoles(lambdaRolesFn, data);
  //   expect(missing).to.deep.equal(['role2']);
  // });

  it('parseMultiFormData should parse base64-encoded body', async () => {
    const boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW';
    const body =
      `------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n` +
      `Content-Disposition: form-data; name="field1"\r\n\r\nvalue1\r\n` +
      `------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n` +
      `Content-Disposition: form-data; name="file"; filename="test.txt"\r\n` +
      `Content-Type: text/plain\r\n\r\nfile content\r\n` +
      `------WebKitFormBoundary7MA4YWxkTrZu0gW--`;
    const event: LambdaEvent = {
      headers: {'content-type': `multipart/form-data; boundary=${boundary}`},
      body: Buffer.from(body).toString('base64'),
      isBase64Encoded: true,
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const result = await lambda.parseMultiFormData(event);
    expect(result.formFields).to.deep.equal({field1: 'value1'});
    expect(result.files).to.have.lengthOf(1);
    expect(result.files[0].filename).to.equal('test.txt');
  });

  it('parseMultiFormData should handle multiple files and fields', async () => {
    const boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW';
    const body =
      `------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n` +
      `Content-Disposition: form-data; name="field1"\r\n\r\nvalue1\r\n` +
      `------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n` +
      `Content-Disposition: form-data; name="file1"; filename="a.txt"\r\n` +
      `Content-Type: text/plain\r\n\r\na\r\n` +
      `------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n` +
      `Content-Disposition: form-data; name="file2"; filename="b.txt"\r\n` +
      `Content-Type: text/plain\r\n\r\nb\r\n` +
      `------WebKitFormBoundary7MA4YWxkTrZu0gW--`;
    const event: LambdaEvent = {
      headers: {'content-type': `multipart/form-data; boundary=${boundary}`},
      body,
      isBase64Encoded: false,
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const result = await lambda.parseMultiFormData(event);
    expect(result.formFields).to.deep.equal({field1: 'value1'});
    expect(result.files).to.have.lengthOf(2);
    expect(result.files.map(f => f.filename))
      .to.include('a.txt')
      .and.include('b.txt');
  });

  it('handler should initialize DB if config.db is set', async () => {
    const initDbStub = sinon
      .stub(require('../../db/db-client'), 'initDb')
      .resolves();
    const lambdaWithDb = new Lambda(
      async () => ({statusCode: 200, response: {}}),
      {db: ['default']},
    );
    const event: LambdaEvent = {
      body: '',
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    await lambdaWithDb.handler(event, {} as LambdaContext);
    expect(initDbStub.called).to.be.true;
    initDbStub.restore();
  });

  it('handler should call validateAccess and handle error', async () => {
    const lambdaWithValidate = new Lambda(
      async () => ({statusCode: 200, response: {}}),
      {
        validateAccess: () => {
          throw Object.assign(new Error('Access denied'), {statusCode: 403});
        },
      },
    );
    const event: LambdaEvent = {
      body: '',
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    const response = await lambdaWithValidate.handler(
      event,
      {} as LambdaContext,
    );
    expect(response.statusCode).to.equal(403);
    expect(response.body).to.include('Access denied');
  });

  it('handler should always call closeLogger in finally', async () => {
    const closeLoggerStub = sinon
      .stub(require('../../utils/logger'), 'closeLogger')
      .resolves();
    const lambdaWithFinally = new Lambda(async () => {
      throw Object.assign(new Error('fail'), {statusCode: 500});
    });
    const event: LambdaEvent = {
      body: '',
      isBase64Encoded: false,
      headers: {},
      multiValueHeaders: {},
      httpMethod: '',
      path: '',
      stageVariables: null,
      resource: '',
    };
    await lambdaWithFinally.handler(event, {} as LambdaContext);
    expect(closeLoggerStub.called).to.be.true;
    closeLoggerStub.restore();
  });

  it('createLambda should return a handler function', () => {
    const handler = createLambda(async () => ({statusCode: 200, response: {}}));
    expect(typeof handler).to.equal('function');
  });
});

describe('getOriginUrl', () => {
  const originalEnv = process.env.NODE_ENV;

  afterEach(() => {
    process.env.NODE_ENV = originalEnv;
  });

  it('should return dev2 url', () => {
    process.env.NODE_ENV = 'dev2';
    expect(getOriginUrl()).to.equal('https://paris2-dev2.fleetship.com');
  });

  it('should return qa2 url', () => {
    process.env.NODE_ENV = 'qa2';
    expect(getOriginUrl()).to.equal('https://paris2-qa2.fleetship.com');
  });

  it('should return uat url', () => {
    process.env.NODE_ENV = 'uat';
    expect(getOriginUrl()).to.equal('https://paris2-uat2.fleetship.com');
  });

  it('should return prod url', () => {
    process.env.NODE_ENV = 'prod';
    expect(getOriginUrl()).to.equal('https://paris2.fleetship.com');
  });

  it('should return default url for unknown env', () => {
    process.env.NODE_ENV = 'somethingelse';
    expect(getOriginUrl()).to.equal('https://default.fleetship.com');
  });

  it('should return default url if NODE_ENV is undefined', () => {
    delete process.env.NODE_ENV;
    expect(getOriginUrl()).to.equal('https://default.fleetship.com');
  });
});

describe('Lambda Class - parseBody', () => {
  let lambda: Lambda;

  beforeEach(() => {
    lambda = new Lambda(async () => ({statusCode: 200, response: {}}));
  });

  it('should return empty objects if body is null', async () => {
    const event = {body: null} as any;
    const result = await (lambda as any).parseBody(event, 'json', null);
    expect(result).to.deep.equal({jsonBody: {}, files: [], formFields: {}});
  });

  it('should parse JSON body when dataType is json', async () => {
    const event = {body: '{"foo":"bar"}', isBase64Encoded: false} as any;
    const result = await (lambda as any).parseBody(event, 'json', event.body);
    expect(result.jsonBody).to.deep.equal({foo: 'bar'});
    expect(result.files).to.deep.equal([]);
    expect(result.formFields).to.deep.equal({});
  });

  it('should throw CustomHttpError if JSON is invalid', async () => {
    const event = {body: '{invalidJson:}', isBase64Encoded: false} as any;
    try {
      await (lambda as any).parseBody(event, 'json', event.body);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err).to.be.instanceOf(CustomHttpError);
      expect(err.statusCode).to.equal(StatusCodes.BAD_REQUEST);
      expect(err.message).to.include('invalid JSON');
    }
  });

  it('should parse file data when dataType is file', async () => {
    // Stub parseMultiFormData to simulate file parsing
    const stub = sinon.stub(lambda, 'parseMultiFormData').resolves({
      files: [{filename: 'a.txt'} as any],
      formFields: {foo: 'bar'},
    });
    const event = {body: 'filedata', isBase64Encoded: false} as any;
    const result = await (lambda as any).parseBody(event, 'file', event.body);
    expect(result.jsonBody).to.deep.equal({});
    expect(result.files).to.deep.equal([{filename: 'a.txt'}]);
    expect(result.formFields).to.deep.equal({foo: 'bar'});
    stub.restore();
  });

  it('should log and throw error for unknown error', async () => {
    sinon.stub(lambda, 'parseJson').throws(new Error('unexpected'));
    const event = {body: '{"foo":"bar"}', isBase64Encoded: false} as any;
    try {
      await (lambda as any).parseBody(event, 'json', event.body);
      expect.fail('Should have thrown');
    } catch (err: any) {
      expect(err).to.be.instanceOf(CustomHttpError);
      expect(err.statusCode).to.equal(StatusCodes.BAD_REQUEST);
      expect(err.message).to.equal(
        'Failed to parse JSON body. Please check the formatting and try again.',
      );
    }
    (lambda.parseJson as sinon.SinonStub).restore();
  });
});
