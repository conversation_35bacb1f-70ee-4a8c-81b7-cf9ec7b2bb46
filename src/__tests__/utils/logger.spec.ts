// Import necessary testing libraries
import {expect, assert} from 'chai';
import sinon from 'sinon';

// Import necessary modules and functions
import {logger, closeLogger} from '../../utils/logger';

describe('Logger Class', () => {
  let loggerInstance: typeof logger;

  beforeEach(() => {
    loggerInstance = logger;
    sinon.restore(); // Restore spies to avoid wrapping issues
  });

  it('should log debug messages correctly', () => {
    const spy = sinon.spy(loggerInstance as any, 'log');
    loggerInstance.configure({logLevel: 'debug'});
    loggerInstance.debug({
      group: 'testGroup',
      name: 'testName',
      message: 'Debug message',
    });
    expect(spy.calledWith('debug', sinon.match.object)).to.be.true;
  });

  it('should not log debug messages if logLevel is not debug', () => {
    const spy = sinon.spy(loggerInstance as any, 'log');
    loggerInstance.configure({logLevel: 'info'});
    loggerInstance.debug({
      group: 'testGroup',
      name: 'testName',
      message: 'Debug message',
    });
    expect(spy.called).to.be.false;
  });

  it('should log info messages correctly', () => {
    const spy = sinon.spy(loggerInstance as any, 'log');
    loggerInstance.info({
      group: 'testGroup',
      name: 'testName',
      message: 'Info message',
    });
    expect(spy.calledWith('info', sinon.match.object)).to.be.true;
  });

  it('should log warn messages correctly', () => {
    const spy = sinon.spy(loggerInstance as any, 'log');
    loggerInstance.warn({
      group: 'testGroup',
      name: 'testName',
      message: 'Warn message',
    });
    expect(spy.calledWith('warn', sinon.match.object)).to.be.true;
  });

  it('should log error messages correctly', () => {
    const spy = sinon.spy(loggerInstance as any, 'log');
    loggerInstance.error({
      group: 'testGroup',
      name: 'testName',
      message: 'Error message',
    });
    expect(spy.calledWith('error', sinon.match.object)).to.be.true;
  });

  it('should configure logger config', () => {
    loggerInstance.configure({
      logLevel: 'debug',
      logstashHost: 'host',
      logstashFields: {foo: 'bar'},
    });
    expect((loggerInstance as any).config.logLevel).to.equal('debug');
    expect((loggerInstance as any).config.logstashHost).to.equal('host');
    expect((loggerInstance as any).config.logstashFields).to.deep.equal({
      foo: 'bar',
    });
  });

  it('should return the same Logger instance (singleton)', () => {
    const logger1 = require('../../utils/logger').logger;
    const logger2 = require('../../utils/logger').logger;
    expect(logger1).to.equal(logger2);
  });

  it('should call initLogger and set config', async () => {
    await require('../../utils/logger').initLogger({
      logLevel: 'debug',
      logstashFields: {a: 1},
    });
    expect((loggerInstance as any).config.logLevel).to.equal('debug');
    expect((loggerInstance as any).config.logstashFields).to.deep.equal({a: 1});
  });

  it('should resolve closeLogger', async () => {
    await require('../../utils/logger')
      .closeLogger()
      .then(() => expect(true).to.be.true)
      .catch(() => expect.fail('Promise was rejected'));
  });
});
