// Import MasterDataType from enums
import {MasterDataType} from '../../enums';
import {expect} from 'chai';
import sinon from 'sinon';
import CategoryController from '../../controller/category.controller';
import {CategoryAttributes} from '../../types/category.types';
import {Category} from '../../models';

describe('CategoryController Tests', () => {
  beforeEach(() => {});

  afterEach(() => {
    sinon.restore();
  });

  it('should return categories when search is empty', async () => {
    const mockCategories = [
      {id: 1, name: 'Category 1', type: MasterDataType.LIST},
      {id: 2, name: 'Category 2', type: MasterDataType.LIST},
    ];
    sinon.stub(Category, 'findAll').resolves(mockCategories as any);

    const result = await CategoryController.list('');
    expect(result).to.deep.equal(mockCategories);
  });

  it('should return filtered categories when search is provided', async () => {
    const mockCategories: CategoryAttributes[] = [
      {
        id: 1,
        name: 'Category 1',
        status: 1,
        type: MasterDataType.LIST,
      },
    ];
    sinon.stub(Category, 'findAll').resolves(mockCategories as any);

    const result = await CategoryController.list('Category 1');
    expect(result).to.deep.equal(mockCategories);
  });

  it('should handle errors gracefully', async () => {
    const errorMessage = 'Database error';
    sinon.stub(Category, 'findAll').throws(new Error(errorMessage));
    try {
      await CategoryController.list('');
    } catch (error) {
      expect((error as Error).message).to.equal(errorMessage);
    }
  });
});
