// Import necessary modules and dependencies
import {expect} from 'chai';
import sinon from 'sinon';
import HazardsController from '../../controller/hazards.controller';
import {HazardAttributes} from '../../types/hazards';
import {Hazard} from '../../models';

describe('HazardsController Tests', () => {
  beforeEach(() => {});

  afterEach(() => {
    sinon.restore();
  });

  it('should return hazards when search is empty', async () => {
    const mockHazards: HazardAttributes[] = [
      {
        id: 1,
        name: 'Hazard 1',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 2,
        name: 'Hazard 2',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    sinon.stub(Hazard, 'findAll').resolves(mockHazards as any);

    const result = await HazardsController.list('');
    expect(result).to.deep.equal(mockHazards);
  });

  it('should return filtered hazards when search is provided', async () => {
    const mockHazards: HazardAttributes[] = [
      {
        id: 1,
        name: 'Hazard 1',
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    sinon.stub(Hazard, 'findAll').resolves(mockHazards as any);

    const result = await HazardsController.list('Hazard 1');
    expect(result).to.deep.equal(mockHazards);
  });

  it('should handle errors gracefully', async () => {
    sinon.stub(Hazard, 'findAll').throws(new Error('Database error'));
    try {
      await HazardsController.list('');
    } catch (error) {
      expect((error as Error).message).to.equal('Database error');
    }
  });
});
