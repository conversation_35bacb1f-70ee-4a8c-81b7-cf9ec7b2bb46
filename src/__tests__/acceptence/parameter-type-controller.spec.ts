// Import necessary modules and dependencies
import {expect} from 'chai';
import sinon from 'sinon';
import ParameterTypeController from '../../controller/parameter-type.controller';
import {ParameterTypeAttributes} from '../../types/parameter-type.types';
import {ParameterType} from '../../models';

describe('ParameterTypeController Tests', () => {
  beforeEach(() => {});

  afterEach(() => {
    sinon.restore();
  });

  it('should return parameter types when search is empty', async () => {
    const mockParameterTypes: ParameterTypeAttributes[] = [
      {
        id: 1,
        name: 'Type 1',
        status: 1,
        is_required_for_risk_rating: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 2,
        name: 'Type 2',
        is_required_for_risk_rating: true,
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    sinon.stub(ParameterType, 'findAll').resolves(mockParameterTypes as any);
    const result = await ParameterTypeController.list('');
    expect(result).to.deep.equal(mockParameterTypes);
  });

  it('should return filtered parameter types when search is provided', async () => {
    const mockParameterTypes: ParameterTypeAttributes[] = [
      {
        id: 1,
        name: 'Type 1',
        is_required_for_risk_rating: true,
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    sinon.stub(ParameterType, 'findAll').resolves(mockParameterTypes as any);
    const result = await ParameterTypeController.list('Type 1');
    expect(result).to.deep.equal(mockParameterTypes);
  });

  it('should add is_required_for_risk_rating to where clause when isRequiredForRiskRating is true', async () => {
    const mockParameterTypes: ParameterTypeAttributes[] = [
      {
        id: 1,
        name: 'Type 1',
        status: 1,
        is_required_for_risk_rating: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    const findAllStub = sinon
      .stub(ParameterType, 'findAll')
      .resolves(mockParameterTypes as any);

    await ParameterTypeController.list('', true);

    // Check that findAll was called with the correct where clause
    const callArgs = findAllStub.getCall(0).args[0];
    expect(callArgs?.where).to.have.property(
      'is_required_for_risk_rating',
      true,
    );
  });

  it('should handle errors gracefully', async () => {
    sinon.stub(ParameterType, 'findAll').throws(new Error('Database error'));
    try {
      await ParameterTypeController.list('');
    } catch (error) {
      expect((error as Error).message).to.equal('Database error');
    }
  });
});
