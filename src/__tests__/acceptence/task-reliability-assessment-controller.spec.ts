// Import necessary modules and dependencies
import {expect} from 'chai';
import sinon from 'sinon';
import TaskReliabilityAssessmentController from '../../controller/task-reliability-assessment.controller';
import {ITaskReliabilityAssessmentAttributes as TaskReliabilityAssessmentAttributes} from '../../types/task-reliability-assessment.types';
import {TaskReliabilityAssessment} from '../../models';

describe('TaskReliabilityAssessmentController Tests', () => {
  beforeEach(() => {});

  afterEach(() => {
    sinon.restore();
  });

  it('should return assessments when search is empty', async () => {
    const mockAssessments: TaskReliabilityAssessmentAttributes[] = [
      {
        id: 1,
        name: 'Assessment 1',
        status: 1,
        options: ['Option 1', 'Option 2'],
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 2,
        name: 'Assessment 2',
        status: 1,
        options: ['Option 3', 'Option 4'],
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    sinon
      .stub(TaskReliabilityAssessment, 'findAll')
      .resolves(mockAssessments as any);

    const result = await TaskReliabilityAssessmentController.list('');
    expect(result).to.deep.equal(mockAssessments);
  });

  it('should return filtered assessments when search is provided', async () => {
    const mockAssessments: TaskReliabilityAssessmentAttributes[] = [
      {
        id: 1,
        name: 'Assessment 1',
        status: 1,
        options: ['Option 1', 'Option 2'],
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    sinon
      .stub(TaskReliabilityAssessment, 'findAll')
      .resolves(mockAssessments as any);

    const result =
      await TaskReliabilityAssessmentController.list('Assessment 1');
    expect(result).to.deep.equal(mockAssessments);
  });

  it('should handle errors gracefully', async () => {
    sinon
      .stub(TaskReliabilityAssessment, 'findAll')
      .throws(new Error('Database error'));
    try {
      await TaskReliabilityAssessmentController.list('');
    } catch (error) {
      expect((error as Error).message).to.equal('Database error');
    }
  });
});
