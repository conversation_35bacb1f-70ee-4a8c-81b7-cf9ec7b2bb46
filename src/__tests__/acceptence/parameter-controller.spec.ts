// Import necessary modules and dependencies
import {expect} from 'chai';
import {MasterDataType} from '../../enums';
import sinon from 'sinon';
import ParameterController from '../../controller/parameter.controller';
import {ParameterAttributes} from '../../types/parameter.types';
import {Parameter} from '../../models';

describe('ParameterController Tests', () => {
  beforeEach(() => {});

  afterEach(() => {
    sinon.restore();
  });

  it('should return parameters when search is empty', async () => {
    const mockParameters: ParameterAttributes[] = [
      {
        id: 1,
        name: 'Parameter 1',
        status: 1,
        parameter_type_id: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 2,
        name: 'Parameter 2',
        status: 1,
        parameter_type_id: 2,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    sinon.stub(Parameter, 'findAll').resolves(mockParameters as any);
    const result = await ParameterController.list('');
    expect(result).to.deep.equal(mockParameters);
  });

  it('should return filtered parameters when search is provided', async () => {
    const mockParameters: ParameterAttributes[] = [
      {
        id: 1,
        name: 'Parameter 1',
        status: 1,
        parameter_type_id: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];
    sinon.stub(Parameter, 'findAll').resolves(mockParameters as any);

    const result = await ParameterController.list('Parameter 1');
    expect(result).to.deep.equal(mockParameters);
  });

  it('should handle errors gracefully', async () => {
    sinon.stub(Parameter, 'findAll').throws(new Error('Database error'));
    try {
      await ParameterController.list('');
    } catch (error) {
      expect((error as Error).message).to.equal('Database error');
    }
  });
});
