import {expect} from 'chai';
import {
  handleError,
  handleValidationError,
  handleNotFoundError,
} from '../../error/index';
import {StatusCodes} from '../../enums';

describe('Error Handlers', () => {
  it('should handle SequelizeValidationError', () => {
    const error = {
      name: 'SequelizeValidationError',
      message: 'Validation failed',
      errors: [
        {path: 'field1', message: 'Field1 is required'},
        {path: 'field2', message: 'Field2 must be a number'},
      ],
    };
    const result = handleError(error);
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
    expect(result.response.message).to.equal('Validation failed');
    expect(result.response.errors).to.deep.equal([
      {field: 'field1', message: 'Field1 is required'},
      {field: 'field2', message: 'Field2 must be a number'},
    ]);
  });

  it('should handle SequelizeForeignKeyConstraintError', () => {
    const error = {
      name: 'SequelizeForeignKeyConstraintError',
      table: 'some_table_name',
    };
    const result = handleError(error);
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
    expect(result.response.message).to.equal(
      'Invalid reference provided for some table name',
    );
  });

  it('should handle unknown errors', () => {
    const error = {name: 'UnknownError', message: 'Something went wrong'};
    const result = handleError(error);
    expect(result.statusCode).to.equal(StatusCodes.INTERNAL_SERVER_ERROR);
    expect(result.response.message).to.equal('Internal server error');
  });

  it('should handle validation errors', () => {
    const result = handleValidationError('Validation failed');
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
    expect(result.response.message).to.equal('Validation failed');
  });

  it('should handle not found errors', () => {
    const result = handleNotFoundError('Resource');
    expect(result.statusCode).to.equal(StatusCodes.NOT_FOUND);
    expect(result.response.message).to.equal('Resource not found');
  });

  it('should create a CustomHttpError with correct status and message', () => {
    const err = new (require('../../error/index').CustomHttpError)(
      StatusCodes.BAD_REQUEST,
      'Custom error occurred',
    );
    expect(err).to.be.instanceOf(Error);
    expect(err).to.have.property('statusCode', StatusCodes.BAD_REQUEST);
    expect(err.response).to.deep.equal({message: 'Custom error occurred'});
    expect(err.message).to.equal('Custom error occurred');
  });
});
