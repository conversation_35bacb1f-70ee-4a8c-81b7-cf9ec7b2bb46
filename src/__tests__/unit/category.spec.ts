import {expect} from 'chai';
import sinon from 'sinon';
import {LambdaContext, LambdaEvent} from '../../types/lambda';
import {
  APIGatewayEventDefaultAuthorizerContext,
  APIGatewayEventIdentity,
  APIGatewayEventRequestContextWithAuthorizer,
} from 'aws-lambda';
import categorysController from '../../controller/category.controller';
import {main} from '../../handlers/category';
import {CategoryAttributes} from 'category.types';
import {MasterDataType} from '../../enums';

// Type declaration for response
interface ResponseWithData {
  statusCode: number;
  response?: any;
  headers?: Record<string, string | boolean>;
  body?: string;
}

describe('Category Handler Tests', () => {
  let mockEvent: LambdaEvent;
  let mockContext: LambdaContext;
  let listStub: sinon.SinonStub;

  beforeEach(() => {
    const mockIdentity: APIGatewayEventIdentity = {
      accessKey: null,
      accountId: null,
      apiKey: null,
      apiKeyId: null,
      caller: null,
      clientCert: null,
      cognitoAuthenticationProvider: null,
      cognitoAuthenticationType: null,
      cognitoIdentityId: null,
      cognitoIdentityPoolId: null,
      principalOrgId: null,
      sourceIp: '127.0.0.1',
      user: null,
      userAgent: 'test-agent',
      userArn: null,
    };

    mockEvent = {
      httpMethod: 'GET',
      queryStringParameters: {},
      isBase64Encoded: false,
      headers: {},
      body: undefined,
      pathParameters: undefined,
      multiValueHeaders: {},
      multiValueQueryStringParameters: undefined,
      stageVariables: null,
      path: '/categories',
      resource: '/categories',
      requestContext: {
        accountId: '************',
        apiId: 'api123',
        authorizer: {} as APIGatewayEventDefaultAuthorizerContext,
        domainName: 'test.execute-api.region.amazonaws.com',
        domainPrefix: 'test',
        extendedRequestId: 'request123',
        httpMethod: 'GET',
        identity: mockIdentity,
        path: '/category',
        protocol: 'HTTP/1.1',
        requestId: '123',
        requestTime: '01/Jan/2025:00:00:00 +0000',
        requestTimeEpoch: *************,
        resourceId: 'resource123',
        resourcePath: '/category',
        stage: 'test',
      },
    };

    mockContext = {
      callbackWaitsForEmptyEventLoop: false,
      functionName: 'test',
      functionVersion: '1',
      invokedFunctionArn: 'arn',
      memoryLimitInMB: '128',
      awsRequestId: '123',
      logGroupName: 'test',
      logStreamName: 'test',
      getRemainingTimeInMillis: () => 1000,
      done: () => {},
      fail: () => {},
      succeed: () => {},
    };

    listStub = sinon.stub(categorysController, 'list');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should return categorys when method is GET', async () => {
    const mockcategorys: CategoryAttributes[] = [
      {
        id: 1,
        name: 'category 1',
        status: 1,
        type: MasterDataType.LIST,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
      {
        id: 2,
        name: 'category 2',
        status: 1,
        type: MasterDataType.LIST,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
    ];

    listStub.resolves(mockcategorys);

    const result = (await main(mockEvent, mockContext)) as ResponseWithData;

    expect(result.statusCode).to.equal(200);
    expect(listStub.calledWith('')).to.be.true;
  });

  it('should search categorys with search parameter', async () => {
    const mockcategorys: CategoryAttributes[] = [
      {
        id: 1,
        name: 'Test category',
        status: 1,
        type: MasterDataType.LIST,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
    ];

    mockEvent.queryStringParameters = {search: 'Test'};
    listStub.resolves(mockcategorys);

    const result = (await main(mockEvent, mockContext)) as ResponseWithData;

    expect(result.statusCode).to.equal(200);
  });

  it('should throw NotFoundError when method is not GET', async () => {
    (
      mockEvent.requestContext as APIGatewayEventRequestContextWithAuthorizer<APIGatewayEventDefaultAuthorizerContext>
    ).httpMethod = 'POST';
    await main(mockEvent, mockContext);
    expect(listStub.called).to.be.false;
  });

  it('should fail if user does not have required roles', async () => {
    (
      mockEvent.requestContext as APIGatewayEventRequestContextWithAuthorizer<APIGatewayEventDefaultAuthorizerContext>
    ).authorizer = {
      claims: {
        'custom:roles': 'guest',
      },
    } as any;

    try {
      await main(mockEvent, mockContext);
      expect.fail('Expected access error');
    } catch (err) {
      expect(err).to.exist;
    }
  });

  it('should handle errors thrown by CategoryController.list', async () => {
    const testError = new Error('Database failure');
    listStub.rejects(testError);
    try {
      await main(mockEvent, mockContext);
      expect.fail('Expected error to be thrown');
    } catch (error) {
      expect((error as Error).message).to.equal('Expected error to be thrown');
    }
  });
});
