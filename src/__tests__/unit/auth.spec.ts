import {expect} from 'chai';
import sinon from 'sinon';
import type {Context} from 'aws-lambda';
import {authenticate} from '../../handlers/auth';
import {bearerToken, bearerTokenInvalid} from '../data/auth-user.data';

describe('authenticate', () => {
  const OLD_ENV = process.env;

  function setupEnv() {
    process.env.JWT_PUBLIC_KEY = 'false';
    process.env.JWT_SECRET =
      '7e3a1fa5e4606d2481096e59ec085a5086cfe5c95bfa95f3f5be40607ae9cac66656cec374c91c7765ee8d0c9940aa0d25ef41a6f9ab1dcdd7ddd5796ddb4484dc21c9768b2ca3eec37353552fc7e4fb0a67f5c6be6aa962e4c3a51f45ed4de994e986359d43832dcd0a8efe8fe9263c1a0366e1bae248b26c04298bfa65f57b920cb4a79f774b24d42cc5818c0b41366589625789429b0f5709d8891e2c6553868894a4a3cf926bad28562af187cf880fc71097b0e2670f6da42ea3f2cad3bf6c5a397f6033e792d150aa1731538fc2556a6f239b10b8805e67c3062879a4c465a406b6c6422fc0363702ef1405f854bed4969d98ca4ae417eb8ddbf5ebf901';
    process.env.JWT_ISSUER = 'https://test.com/auth';
  }

  beforeEach(() => {
    // Reset env before each test
    process.env = {...OLD_ENV};
    setupEnv();
  });

  afterEach(() => {
    sinon.restore();
    process.env = OLD_ENV;
  });

  const createEvent = (
    token: string,
    arn = 'arn:aws:execute-api:region:accid:api-id/stage/verb/resource',
  ) => ({
    headers: {
      authorization: `Bearer ${token}`,
    },
    methodArn: arn,
  });

  const mockContext = {} as Context;

  it('should return Unauthorized if no token provided', done => {
    const event = {headers: {}};
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Unauthorized');
      expect(result).to.be.undefined;
      done();
    });
  });

  it('should return error Unauthorized if invalid token pass', done => {
    const event = createEvent(bearerTokenInvalid);
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Unauthorized');
      expect(result).to.be.undefined;
      done();
    });
  });

  it('should return Unauthorized on invalid token', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const verifyStub = sinon
      .stub()
      .callsArgWith(3, new Error('Invalid token'), null);
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = createEvent('invalidtoken');
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Unauthorized');
      expect(result).to.be.undefined;
      done();
    });
  });

  it('should return Forbidden if roles not allowed', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const fakePayload = {
      realm_access: {roles: ['unauthorized|role']},
      user_id: '123',
      preferred_username: 'john.doe',
    };

    const verifyStub = sinon.stub().callsArgWith(3, null, fakePayload);
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = createEvent(bearerToken);
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Forbidden');
      expect(result).to.be.undefined;
      done();
    });
  });

  it('should return Allow policy for valid token and roles', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const validPayload = {
      user_id: '123',
      preferred_username: 'john.doe',
      realm_access: {roles: ['nbp|insp|view']},
      ship_party_id: 'ship123',
      ship_party_type: 'typeA',
    };

    const verifyStub = sinon.stub().callsArgWith(3, null, validPayload);
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = createEvent('validtoken');
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.be.null;
      expect(result).to.have.property('principalId', '123');
      //   expect(result.policyDocument.Statement[0].Effect).to.equal('Allow');
      //   expect(result.context.roles).to.include('nbp|insp|view');
      done();
    });
  });

  it('should construct ARN from requestContext if methodArn not provided', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const validPayload = {
      user_id: '123',
      preferred_username: 'john.doe',
      realm_access: {roles: ['vessel|report|edit']},
    };

    const verifyStub = sinon.stub().callsArgWith(3, null, validPayload);
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = {
      headers: {authorization: 'Bearer token'},
      requestContext: {
        accountId: '************',
        apiId: 'abcdef123',
        stage: 'dev',
      },
    };

    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.be.null;
      //   expect(result.policyDocument.Statement[0].Resource).to.include(
      //     'arn:aws:execute-api:us-east-1:************:abcdef123/dev/*',
      //   );
      done();
    });
  });

  it('should return Unauthorized if Authorization header is present but empty', done => {
    const event = {headers: {Authorization: ''}};
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Unauthorized');
      expect(result).to.be.undefined;
      done();
    });
  });

  it('should handle token without Bearer prefix', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const validPayload = {
      user_id: '123',
      preferred_username: 'john.doe',
      realm_access: {roles: ['nbp|insp|view']},
    };
    const verifyStub = sinon.stub().callsArgWith(3, null, validPayload);
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = {
      headers: {authorization: 'tokenwithoutbearer'},
      methodArn: 'arn:aws:execute-api:region:accid:api-id/stage/verb/resource',
    };
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.be.null;
      expect(result).to.have.property('principalId', '123');
      done();
    });
  });

  it('should return Unauthorized if decoded is a string', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const verifyStub = sinon.stub().callsArgWith(3, null, 'somestring');
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = {
      headers: {authorization: 'Bearer sometoken'},
      methodArn: 'arn:aws:execute-api:region:accid:api-id/stage/verb/resource',
    };
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Unauthorized');
      expect(result).to.be.undefined;
      done();
    });
  });

  it('should return Unauthorized if decoded is falsy', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const verifyStub = sinon.stub().callsArgWith(3, null, null);
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = {
      headers: {authorization: 'Bearer sometoken'},
      methodArn: 'arn:aws:execute-api:region:accid:api-id/stage/verb/resource',
    };
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Unauthorized');
      expect(result).to.be.undefined;
      done();
    });
  });

  it('should return Forbidden if userRoles is empty', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const validPayload = {
      user_id: '123',
      preferred_username: 'john.doe',
      realm_access: {roles: []},
    };
    const verifyStub = sinon.stub().callsArgWith(3, null, validPayload);
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = {
      headers: {authorization: 'Bearer sometoken'},
      methodArn: 'arn:aws:execute-api:region:accid:api-id/stage/verb/resource',
    };
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Forbidden');
      expect(result).to.be.undefined;
      done();
    });
  });

  it('should handle routeArn for serverless offline', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const validPayload = {
      user_id: '123',
      preferred_username: 'john.doe',
      realm_access: {roles: ['nbp|insp|view']},
    };
    const verifyStub = sinon.stub().callsArgWith(3, null, validPayload);
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = {
      headers: {authorization: 'Bearer sometoken'},
      routeArn: 'arn:aws:execute-api:region:accid:api-id/stage',
    };
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.be.null;
      expect(result).to.have.property('principalId', '123');
      done();
    });
  });

  it('should return Unauthorized if no ARN or requestContext is present', done => {
    process.env.JWT_PUBLIC_KEY =
      '-----BEGIN PUBLIC KEY-----\nfakekey\n-----END PUBLIC KEY-----';
    const validPayload = {
      user_id: '123',
      preferred_username: 'john.doe',
      realm_access: {roles: ['nbp|insp|view']},
    };
    const verifyStub = sinon.stub().callsArgWith(3, null, validPayload);
    sinon.replace(require('jsonwebtoken'), 'verify', verifyStub);

    const event = {
      headers: {authorization: 'Bearer sometoken'},
    };
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Unauthorized');
      expect(result).to.be.undefined;
      done();
    });
  });

  it('should handle error thrown in main try/catch', done => {
    // Simulate error in verify by throwing in the main try/catch
    const event = {
      headers: {authorization: 'Bearer sometoken'},
      methodArn: 'arn:aws:execute-api:region:accid:api-id/stage/verb/resource',
    };
    // Replace verify to throw
    sinon.replace(require('jsonwebtoken'), 'verify', () => {
      throw new Error('Unexpected error');
    });
    authenticate(event as any, mockContext, (error, result) => {
      expect(error).to.equal('Unauthorized');
      expect(result).to.be.undefined;
      done();
    });
  });
});
