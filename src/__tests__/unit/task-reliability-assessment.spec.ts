import {expect} from 'chai';
import sinon from 'sinon';
import {LambdaContext, LambdaEvent} from '../../types/lambda';
import {
  APIGatewayEventDefaultAuthorizerContext,
  APIGatewayEventIdentity,
  APIGatewayEventRequestContextWithAuthorizer,
} from 'aws-lambda';
import TaskReliabilityAssessmentController from '../../controller/task-reliability-assessment.controller';
import {main} from '../../handlers/task-reliability-assessment';
import {TaskReliabilityAssessmentAttributes} from 'task-reliability-assessment';

// Type declaration for response
interface ResponseWithData {
  statusCode: number;
  response?: any;
  headers?: Record<string, string | boolean>;
  body?: string;
}

describe('Task Reliability Assessment Handler Tests', () => {
  let mockEvent: LambdaEvent;
  let mockContext: LambdaContext;
  let listStub: sinon.SinonStub;

  beforeEach(() => {
    const mockIdentity: APIGatewayEventIdentity = {
      accessKey: null,
      accountId: null,
      apiKey: null,
      apiKeyId: null,
      caller: null,
      clientCert: null,
      cognitoAuthenticationProvider: null,
      cognitoAuthenticationType: null,
      cognitoIdentityId: null,
      cognitoIdentityPoolId: null,
      principalOrgId: null,
      sourceIp: '127.0.0.1',
      user: null,
      userAgent: 'test-agent',
      userArn: null,
    };

    mockEvent = {
      httpMethod: 'GET',
      queryStringParameters: {},
      isBase64Encoded: false,
      headers: {},
      body: undefined,
      pathParameters: undefined,
      multiValueHeaders: {},
      multiValueQueryStringParameters: undefined,
      stageVariables: null,
      path: '/task-reliability-assessment',
      resource: '/task-reliability-assessment',
      requestContext: {
        accountId: '************',
        apiId: 'api123',
        authorizer: {} as APIGatewayEventDefaultAuthorizerContext,
        domainName: 'test.execute-api.region.amazonaws.com',
        domainPrefix: 'test',
        extendedRequestId: 'request123',
        httpMethod: 'GET',
        identity: mockIdentity,
        path: '/task-reliability-assessment',
        protocol: 'HTTP/1.1',
        requestId: '123',
        requestTime: '01/Jan/2025:00:00:00 +0000',
        requestTimeEpoch: *************,
        resourceId: 'resource123',
        resourcePath: '/task-reliability-assessment',
        stage: 'test',
      },
    };

    mockContext = {
      callbackWaitsForEmptyEventLoop: false,
      functionName: 'test',
      functionVersion: '1',
      invokedFunctionArn: 'arn',
      memoryLimitInMB: '128',
      awsRequestId: '123',
      logGroupName: 'test',
      logStreamName: 'test',
      getRemainingTimeInMillis: () => 1000,
      done: () => {},
      fail: () => {},
      succeed: () => {},
    };

    listStub = sinon.stub(TaskReliabilityAssessmentController, 'list');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should return Reliability when method is GET', async () => {
    const mockReliability: TaskReliabilityAssessmentAttributes[] = [
      {
        id: 1,
        name: 'reliability 1',
        options: {severity: 'high', likelihood: 'low'}, // example options
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
      {
        id: 2,
        name: 'reliability 2',
        options: {severity: 'medium', likelihood: 'medium'}, // example options
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
    ];

    listStub.resolves(mockReliability);

    const result = (await main(mockEvent, mockContext)) as ResponseWithData;

    expect(result.statusCode).to.equal(200);
    expect(listStub.calledWith('')).to.be.true;
  });

  it('should search Reliability with search parameter', async () => {
    const mockReliability: TaskReliabilityAssessmentAttributes[] = [
      {
        id: 1,
        name: 'reliability 1',
        options: {severity: 'high', likelihood: 'low'}, // example options
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
      {
        id: 2,
        name: 'reliability 2',
        options: {severity: 'medium', likelihood: 'medium'}, // example options
        status: 1,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
    ];

    mockEvent.queryStringParameters = {search: 'Test'};
    listStub.resolves(mockReliability);

    const result = (await main(mockEvent, mockContext)) as ResponseWithData;

    expect(result.statusCode).to.equal(200);
  });

  it('should throw NotFoundError when method is not GET', async () => {
    (
      mockEvent.requestContext as APIGatewayEventRequestContextWithAuthorizer<APIGatewayEventDefaultAuthorizerContext>
    ).httpMethod = 'POST';
    await main(mockEvent, mockContext);
    expect(listStub.called).to.be.false;
  });

  it('should fail if user does not have required roles', async () => {
    (
      mockEvent.requestContext as APIGatewayEventRequestContextWithAuthorizer<APIGatewayEventDefaultAuthorizerContext>
    ).authorizer = {
      claims: {
        'custom:roles': 'guest',
      },
    } as any;

    try {
      await main(mockEvent, mockContext);
      expect.fail('Expected access error');
    } catch (err) {
      expect(err).to.exist;
    }
  });
});
