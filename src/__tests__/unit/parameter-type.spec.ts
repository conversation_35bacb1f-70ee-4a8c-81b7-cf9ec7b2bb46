import {expect} from 'chai';
import sinon from 'sinon';
import {LambdaContext, LambdaEvent} from '../../types/lambda';
import {
  APIGatewayEventDefaultAuthorizerContext,
  APIGatewayEventIdentity,
  APIGatewayEventRequestContextWithAuthorizer,
} from 'aws-lambda';
import parameterController from '../../controller/parameter-type.controller';
import {main} from '../../handlers/parameter-type';
import {ParameterTypeAttributes} from 'parameter-type.types';

// Type declaration for response
interface ResponseWithData {
  statusCode: number;
  response?: any;
  headers?: Record<string, string | boolean>;
  body?: string;
}

describe('Parameter Type Handler Tests', () => {
  let mockEvent: LambdaEvent;
  let mockContext: LambdaContext;
  let listStub: sinon.SinonStub;

  beforeEach(() => {
    const mockIdentity: APIGatewayEventIdentity = {
      accessKey: null,
      accountId: null,
      apiKey: null,
      apiKeyId: null,
      caller: null,
      clientCert: null,
      cognitoAuthenticationProvider: null,
      cognitoAuthenticationType: null,
      cognitoIdentityId: null,
      cognitoIdentityPoolId: null,
      principalOrgId: null,
      sourceIp: '127.0.0.1',
      user: null,
      userAgent: 'test-agent',
      userArn: null,
    };

    mockEvent = {
      httpMethod: 'GET',
      queryStringParameters: {},
      isBase64Encoded: false,
      headers: {},
      body: undefined,
      pathParameters: undefined,
      multiValueHeaders: {},
      multiValueQueryStringParameters: undefined,
      stageVariables: null,
      path: '/parameter-type',
      resource: '/parameter-type',
      requestContext: {
        accountId: '************',
        apiId: 'api123',
        authorizer: {} as APIGatewayEventDefaultAuthorizerContext,
        domainName: 'test.execute-api.region.amazonaws.com',
        domainPrefix: 'test',
        extendedRequestId: 'request123',
        httpMethod: 'GET',
        identity: mockIdentity,
        path: '/parameter-type',
        protocol: 'HTTP/1.1',
        requestId: '123',
        requestTime: '01/Jan/2025:00:00:00 +0000',
        requestTimeEpoch: *************,
        resourceId: 'resource123',
        resourcePath: '/parameter-type',
        stage: 'test',
      },
    };

    mockContext = {
      callbackWaitsForEmptyEventLoop: false,
      functionName: 'test',
      functionVersion: '1',
      invokedFunctionArn: 'arn',
      memoryLimitInMB: '128',
      awsRequestId: '123',
      logGroupName: 'test',
      logStreamName: 'test',
      getRemainingTimeInMillis: () => 1000,
      done: () => {},
      fail: () => {},
      succeed: () => {},
    };

    listStub = sinon.stub(parameterController, 'list');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should return parameter type when method is GET', async () => {
    const mockParameterType: ParameterTypeAttributes[] = [
      {
        id: 1,
        name: 'ParameterType 1',
        status: 1,
        is_required_for_risk_rating: true,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
      {
        id: 2,
        name: 'ParameterType 1',
        status: 1,
        is_required_for_risk_rating: true,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
    ];

    listStub.resolves(mockParameterType);

    const result = (await main(mockEvent, mockContext)) as ResponseWithData;

    expect(result.statusCode).to.equal(200);
    expect(listStub.calledWith('')).to.be.true;
  });

  it('should search parameter type with search parameter', async () => {
    const mockParameterType: ParameterTypeAttributes[] = [
      {
        id: 1,
        name: 'Test Parameter',
        status: 1,
        is_required_for_risk_rating: true,
        created_at: new Date(),
        updated_at: new Date(),
        created_by: 'user1',
        updated_by: 'user1',
      },
    ];

    mockEvent.queryStringParameters = {search: 'Test'};
    listStub.resolves(mockParameterType);

    const result = (await main(mockEvent, mockContext)) as ResponseWithData;

    expect(result.statusCode).to.equal(200);
  });

  it('should throw NotFoundError when method is not GET', async () => {
    (
      mockEvent.requestContext as APIGatewayEventRequestContextWithAuthorizer<APIGatewayEventDefaultAuthorizerContext>
    ).httpMethod = 'POST';
    await main(mockEvent, mockContext);
    expect(listStub.called).to.be.false;
  });

  it('should fail if user does not have required roles', async () => {
    (
      mockEvent.requestContext as APIGatewayEventRequestContextWithAuthorizer<APIGatewayEventDefaultAuthorizerContext>
    ).authorizer = {
      claims: {
        'custom:roles': 'guest',
      },
    } as any;
    try {
      await main(mockEvent, mockContext);
      expect.fail('Expected access error');
    } catch (err) {
      expect(err).to.exist;
    }
  });

  it('should handle errors thrown by ParameterType.list', async () => {
    const testError = new Error('Database failure');
    listStub.rejects(testError);
    try {
      await main(mockEvent, mockContext);
      expect.fail('Expected error to be thrown');
    } catch (error) {
      expect((error as Error).message).to.equal('Expected error to be thrown');
    }
  });
});
