import {expect} from 'chai';
import sinon from 'sinon';
import axios from 'axios';
import {MasterDataType} from '../../enums/master-data-type.enum';
import {BasicStatus, RiskAssessmentStatus} from '../../enums';
import * as TemplateValidation from '../../validation/template.validation';
import {sq} from '../../db/db-client';
import VesselMasterDataService from '../../controller/paris2-api-controller';
import * as TemplateDto from '../../dtos/template.dto';
import {
  Category,
  Hazard,
  Parameter,
  Template,
  TemplateCategory,
  TemplateHazard,
  TemplateJob,
  TemplateJobInitialRiskRating,
  TemplateJobResidualRiskRating,
  TemplateParameter,
  TemplateTaskReliabilityAssessment,
} from '../../models';
import TemplateKeyword from '../../models/template-keyword.model';
import {
  createTemplateHandler,
  deleteTemplateHandler,
  getTemplateByIdHand<PERSON>,
  getTemplate<PERSON>ist<PERSON>and<PERSON>,
  parseQueryParams,
  patchTemplateHandler,
  getTemplateUsersHandler,
} from '../../handlers/template';
import {StatusCodes} from '../../enums';
import {LambdaContext, LambdaEvent} from 'lambda';
import {
  APIGatewayEventDefaultAuthorizerContext,
  APIGatewayEventIdentity,
  APIGatewayEventRequestContextWithAuthorizer,
} from 'aws-lambda';

describe('Template Handler Tests', () => {
  let transactionStub: any;
  let transaction: any;
  let mockReqContext: APIGatewayEventRequestContextWithAuthorizer<APIGatewayEventDefaultAuthorizerContext>;
  let mockEvent: LambdaEvent;
  let mockContext: LambdaContext;
  const templateInput = {
    task_requiring_ra: 'Test task',
    task_duration: 3,
    task_duration_unit: 'hours',
    task_alternative_consideration: 'Test alt',
    task_rejection_reason: 'None',
    worst_case_scenario: 'Explosion',
    recovery_measures: 'Evacuation',
    status: 'PUBLISHED',
    template_keyword: ['safety', 'hazard'],
    template_category: {category_id: [1, 2]},
    template_hazard: {
      is_other: true,
      hazard_id: [10, 11],
      value: 'Test Hazard',
    },
    parameters: [
      {
        is_other: true,
        parameter_type_id: 1,
        parameter_id: [1],
        value: 'Test Parameter',
      },
    ],
    template_job: [
      {
        job_step: 'Disconnect power supply',
        job_hazard: 'Accidental contact with live wire',
        job_nature_of_risk: 'Electric shock',
        job_existing_control: 'Use insulated tools and PPE',
        job_additional_mitigation: 'Assign safety observer',
        job_close_out_date: '2025-05-21',
        job_close_out_responsibility_id: '1',
        template_job_initial_risk_rating: [
          {
            parameter_type_id: 1,
            rating: 'A1',
          },
        ],
        template_job_residual_risk_rating: [
          {
            parameter_type_id: 1,
            rating: 'A1',
          },
        ],
      },
    ],
    template_task_reliability_assessment: [
      {
        task_reliability_assessment_id: 1,
        task_reliability_assessment_answer: 'Yes',
        condition: 'Operator trained and certified',
      },
    ],
  };

  beforeEach(() => {
    transaction = {
      commit: sinon.stub(),
      rollback: sinon.stub().resolves(),
    };

    transactionStub = sinon.stub(sq, 'transaction').resolves(transaction);
    const mockIdentity: APIGatewayEventIdentity = {
      accessKey: null,
      accountId: null,
      apiKey: null,
      apiKeyId: null,
      caller: null,
      clientCert: null,
      cognitoAuthenticationProvider: null,
      cognitoAuthenticationType: null,
      cognitoIdentityId: null,
      cognitoIdentityPoolId: null,
      principalOrgId: null,
      sourceIp: '127.0.0.1',
      user: null,
      userAgent: 'test-agent',
      userArn: null,
    };

    mockReqContext = {
      accountId: '************',
      apiId: 'api123',
      authorizer: {
        user_id: 'user-123',
      } as APIGatewayEventDefaultAuthorizerContext,
      domainName: 'test.execute-api.region.amazonaws.com',
      domainPrefix: 'test',
      extendedRequestId: 'request123',
      httpMethod: 'POST',
      identity: mockIdentity,
      path: '/templates',
      protocol: 'HTTP/1.1',
      requestId: '123',
      requestTime: '01/Jan/2025:00:00:00 +0000',
      requestTimeEpoch: *************,
      resourceId: 'resource123',
      resourcePath: '/test',
      stage: 'test',
    };

    mockEvent = {
      httpMethod: 'POST',
      queryStringParameters: {},
      isBase64Encoded: false,
      headers: {},
      body: undefined,
      pathParameters: undefined,
      multiValueHeaders: {},
      multiValueQueryStringParameters: undefined,
      stageVariables: null,
      path: '/templates',
      resource: '/templates',
      requestContext: mockReqContext,
    };

    mockContext = {
      callbackWaitsForEmptyEventLoop: false,
      functionName: 'test',
      functionVersion: '1',
      invokedFunctionArn: 'arn',
      memoryLimitInMB: '128',
      awsRequestId: '123',
      logGroupName: 'test',
      logStreamName: 'test',
      getRemainingTimeInMillis: () => 1000,
      done: () => {},
      fail: () => {},
      succeed: () => {},
    };
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should create a new template and return 201 response', async () => {
    mockEvent.body = JSON.stringify(templateInput);
    const templateRecord = {id: 999, ...templateInput};
    sinon.stub(TemplateValidation, 'validateTemplate').returns([]);
    const createTemplateStub = sinon
      .stub(Template, 'create')
      .resolves(templateRecord as any);
    sinon.stub(TemplateKeyword, 'create').resolves();
    sinon.stub(TemplateCategory, 'create').resolves();
    sinon.stub(Hazard, 'create').resolves({id: 20} as any);
    sinon.stub(TemplateHazard, 'create').resolves();
    sinon.stub(Parameter, 'create').resolves({id: 20} as any);
    sinon.stub(TemplateParameter, 'create').resolves();
    sinon.stub(TemplateJob, 'create').resolves({id: 1} as any);
    sinon.stub(TemplateJobInitialRiskRating, 'create').resolves();
    sinon.stub(TemplateJobResidualRiskRating, 'create').resolves();
    sinon.stub(TemplateTaskReliabilityAssessment, 'create').resolves();
    const result = await createTemplateHandler(mockEvent, mockContext);
    expect(transactionStub.calledOnce).to.be.true;
    expect(createTemplateStub.calledOnce).to.be.true;
    expect(result.statusCode).to.equal(StatusCodes.CREATED);
  });

  it('should return 400 for validation error', async () => {
    sinon
      .stub(TemplateValidation, 'validateTemplate')
      .returns(['Missing required fields']);
    const result = await createTemplateHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
  });

  it('should return 500 on DB failure and rollback', async () => {
    sinon.stub(TemplateValidation, 'validateTemplate').returns([]);
    sinon.stub(Template, 'create').throws(new Error('DB Error'));
    const result = await createTemplateHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.INTERNAL_SERVER_ERROR);
    expect(transaction.rollback.calledOnce).to.be.true;
  });

  it('should update a template and return 200 response', async () => {
    const inputWithId = {...templateInput, id: 100};
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'PATCH';
    mockReqContext.httpMethod = 'PATCH';
    mockEvent.body = JSON.stringify(inputWithId);
    //-----destroy-------//
    sinon.stub(TemplateValidation, 'validateTemplate').returns([]);
    const templateRecord = {id: 100, ...templateInput};
    const fakeTemplateInstance = {
      id: 100,
      update: sinon.stub().resolves({...templateRecord}),
    };
    sinon.stub(Template, 'findByPk').resolves(fakeTemplateInstance as any);
    sinon.stub(TemplateCategory, 'destroy').resolves();
    sinon.stub(TemplateHazard, 'destroy').resolves();
    sinon.stub(TemplateTaskReliabilityAssessment, 'destroy').resolves();
    sinon.stub(TemplateJob, 'findAll').resolves([{id: 1} as any]);
    sinon.stub(TemplateJobInitialRiskRating, 'destroy').resolves();
    sinon.stub(TemplateJobResidualRiskRating, 'destroy').resolves();
    sinon.stub(TemplateJob, 'destroy').resolves();
    sinon.stub(TemplateKeyword, 'destroy').resolves();
    //-------recreate-------//
    sinon.stub(TemplateKeyword, 'create').resolves();
    sinon.stub(TemplateCategory, 'create').resolves();
    sinon.stub(Hazard, 'create').resolves({id: 20} as any);
    sinon.stub(TemplateHazard, 'create').resolves();
    sinon.stub(Parameter, 'create').resolves({id: 20} as any);
    sinon.stub(TemplateParameter, 'create').resolves();
    sinon.stub(TemplateJob, 'create').resolves({id: 1} as any);
    sinon.stub(TemplateJobInitialRiskRating, 'create').resolves();
    sinon.stub(TemplateJobResidualRiskRating, 'create').resolves();
    sinon.stub(TemplateTaskReliabilityAssessment, 'create').resolves();
    const result = await patchTemplateHandler(mockEvent, mockContext);
    expect(transactionStub.calledOnce).to.be.true;
    expect(result.statusCode).to.equal(StatusCodes.OK);
  });

  it('should throw error for missing template id', async () => {
    const inputWithId = {...templateInput, id: 100};
    mockEvent.httpMethod = 'PATCH';
    mockReqContext.httpMethod = 'PATCH';
    mockEvent.body = JSON.stringify(inputWithId);
    const result = await patchTemplateHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
  });

  it('should throw error for template not found', async () => {
    const inputWithId = {...templateInput, id: 100};
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'PATCH';
    mockReqContext.httpMethod = 'PATCH';
    mockEvent.body = JSON.stringify(inputWithId);
    sinon.stub(Template, 'findByPk').resolves();
    const result = await patchTemplateHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
  });

  it('should return 400 for validation error', async () => {
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'PATCH';
    mockReqContext.httpMethod = 'PATCH';
    sinon.stub(Template, 'findByPk').resolves({id: 1} as any);
    sinon
      .stub(TemplateValidation, 'validateTemplate')
      .returns(['Missing required fields']);
    const result = await patchTemplateHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
  });

  it('should return 500 on DB failure and rollback', async () => {
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'PATCH';
    mockReqContext.httpMethod = 'PATCH';
    sinon.stub(Template, 'findByPk').throws(new Error('DB Error'));
    const result = await patchTemplateHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.INTERNAL_SERVER_ERROR);
    expect(transaction.rollback.calledOnce).to.be.true;
  });

  it('should find the template by id', async () => {
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'GET';
    mockReqContext.httpMethod = 'GET';
    sinon.stub(Template, 'findByPk').resolves({id: 1} as any);
    const result = await getTemplateByIdHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.OK);
  });

  it('should throw an error if id is not passed in path', async () => {
    mockEvent.httpMethod = 'GET';
    mockReqContext.httpMethod = 'GET';
    const result = await getTemplateByIdHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
  });

  it('should throw an error template not found', async () => {
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'GET';
    mockReqContext.httpMethod = 'GET';
    sinon.stub(Template, 'findByPk').resolves();
    const result = await getTemplateByIdHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.NOT_FOUND);
  });

  it('should throw Internal Server Error', async () => {
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'GET';
    mockReqContext.httpMethod = 'GET';
    sinon.stub(Template, 'findByPk').throws('Internal Server Error');
    const result = await getTemplateByIdHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.INTERNAL_SERVER_ERROR);
  });

  it('should delete a template and return 200 response', async () => {
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'DELETE';
    mockReqContext.httpMethod = 'DELETE';
    sinon.stub(Template, 'findByPk').resolves({id: 100} as any);
    sinon.stub(TemplateJob, 'findAll').resolves([{id: 1} as any]);
    sinon.stub(TemplateCategory, 'destroy').resolves();
    sinon.stub(TemplateHazard, 'destroy').resolves();
    sinon.stub(TemplateTaskReliabilityAssessment, 'destroy').resolves();
    sinon.stub(TemplateJobInitialRiskRating, 'destroy').resolves();
    sinon.stub(TemplateJobResidualRiskRating, 'destroy').resolves();
    sinon.stub(TemplateJob, 'destroy').resolves();
    sinon.stub(TemplateKeyword, 'destroy').resolves();
    sinon.stub(Template, 'destroy').resolves();
    const result = await deleteTemplateHandler(mockEvent, mockContext);
    expect(transactionStub.calledOnce).to.be.true;
    expect(result.statusCode).to.equal(StatusCodes.OK);
  });

  it('should give error if id is missing in path', async () => {
    mockEvent.httpMethod = 'DELETE';
    mockReqContext.httpMethod = 'DELETE';
    const result = await deleteTemplateHandler(mockEvent, mockContext);
    expect(transactionStub.calledOnce).to.be.true;
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
  });

  it('should give error if template not found', async () => {
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'DELETE';
    mockReqContext.httpMethod = 'DELETE';
    sinon.stub(Template, 'findByPk').resolves();
    const result = await deleteTemplateHandler(mockEvent, mockContext);
    expect(transactionStub.calledOnce).to.be.true;
    expect(result.statusCode).to.equal(StatusCodes.NOT_FOUND);
  });

  it('should throw Internal Server Error', async () => {
    mockEvent.pathParameters = {id: '100'};
    mockEvent.httpMethod = 'DELETE';
    mockReqContext.httpMethod = 'DELETE';
    sinon.stub(Template, 'findByPk').throws('Internal Server Error');
    const result = await deleteTemplateHandler(mockEvent, mockContext);
    expect(transactionStub.calledOnce).to.be.true;
    expect(result.statusCode).to.equal(StatusCodes.INTERNAL_SERVER_ERROR);
  });

  describe('getTemplateListHandler Tests', () => {
    let mockTemplates: any;
    let categoryStub: sinon.SinonStub;
    let hazardStub: sinon.SinonStub;
    let templateFindStub: sinon.SinonStub;
    let templateCountStub: sinon.SinonStub;
    let vesselServiceStub: sinon.SinonStub;

    beforeEach(() => {
      sinon.restore();

      // Mock template data
      mockTemplates = {
        count: 2,
        rows: [
          {
            id: 1,
            task_requiring_ra: 'Test Task 1',
            created_by: 'user1',
            created_at: '2025-06-06',
            template_category: [{category: {id: 1, name: 'Category 1'}}],
            template_hazards: [{hazard_detail: {id: 1, name: 'Hazard 1'}}],
            template_keywords: [{name: 'keyword1'}],
          },
          {
            id: 2,
            task_requiring_ra: 'Test Task 2',
            created_by: 'user2',
            created_at: '2025-06-07',
            template_category: [{category: {id: 2, name: 'Category 2'}}],
            template_hazards: [{hazard_detail: {id: 2, name: 'Hazard 2'}}],
            template_keywords: [{name: 'keyword2'}],
          },
        ],
      };

      // Setup stubs with proper typing and required fields
      const mockCategories = [
        {
          id: 1,
          name: 'Category 1',
          type: MasterDataType.LIST,
          status: BasicStatus.ACTIVE,
        },
        {
          id: 2,
          name: 'Category 2',
          type: MasterDataType.OTHER,
          status: BasicStatus.ACTIVE,
        },
      ];

      const mockHazards = [
        {
          id: 1,
          name: 'Hazard 1',
          type: MasterDataType.LIST,
          status: BasicStatus.ACTIVE,
        },
        {
          id: 2,
          name: 'Hazard 2',
          type: MasterDataType.OTHER,
          status: BasicStatus.ACTIVE,
        },
      ];

      categoryStub = sinon
        .stub(Category, 'findAll')
        .resolves(mockCategories.map(cat => Category.build(cat)));

      hazardStub = sinon
        .stub(Hazard, 'findAll')
        .resolves(mockHazards.map(haz => Hazard.build(haz)));

      templateFindStub = sinon
        .stub(Template, 'findAll')
        .resolves(mockTemplates.rows as any);
      templateCountStub = sinon
        .stub(Template, 'findAndCountAll')
        .resolves(mockTemplates as any);

      vesselServiceStub = sinon
        .stub(VesselMasterDataService, 'getUserDetails')
        .resolves({
          users: [
            {id: 'user1', email: '<EMAIL>'},
            {id: 'user2', email: '<EMAIL>'},
          ],
        });

      // Setup validation stub
      const validationStub = sinon.stub(TemplateDto, 'getTemplateListSchema');

      validationStub.returns({
        validate: (params: any) => {
          const validSortFields = ['created_at', 'task_requiring_ra'];
          const validSortOrders = ['ASC', 'DESC'];

          if (params.sort_by && !validSortFields.includes(params.sort_by)) {
            return {
              error: {details: [{message: 'Invalid sort field'}]},
            };
          }

          if (
            params.sort_order &&
            !validSortOrders.includes(params.sort_order)
          ) {
            return {
              error: {details: [{message: 'Invalid sort order'}]},
            };
          }

          // Return successful validation for valid parameters
          return {
            value: {
              ...params,
              page: params.page || 1,
              limit: params.limit || 10,
              sort_by: params.sort_by || 'created_at',
              sort_order: params.sort_order || 'ASC',
            },
          };
        },
      });
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return a list of templates with status 200 for various scenarios', async () => {
      const testCases = [
        {
          name: 'with all filters and sorting',
          event: {
            ...mockEvent,
            queryStringParameters: {
              search: 'test',
              sort_by: 'created_at',
              sort_order: 'ASC',
              'created_at[start_date]': '2025-06-06',
              'created_at[end_date]': '2025-06-07',
              status: 2,
            },
            multiValueQueryStringParameters: {
              created_by: ['user1', 'user2'],
              ra_categories: ['1', '2'],
              hazard_categories: ['1', '2'],
            },
          },
        },
        {
          name: 'with pagination',
          event: {
            ...mockEvent,
            queryStringParameters: {
              page: '2',
              limit: '5',
            },
            multiValueQueryStringParameters: {},
          },
        },
        {
          name: 'with search across multiple fields',
          event: {
            ...mockEvent,
            queryStringParameters: {
              search: 'keyword1',
            },
            multiValueQueryStringParameters: {},
          },
        },
        {
          name: 'with category based search',
          event: {
            ...mockEvent,
            queryStringParameters: {
              search: 'Category 1',
            },
            multiValueQueryStringParameters: {},
          },
        },
        {
          name: 'with hazard based search',
          event: {
            ...mockEvent,
            queryStringParameters: {
              search: 'Hazard 1',
            },
            multiValueQueryStringParameters: {},
          },
        },
        {
          name: 'with status filter',
          event: {
            ...mockEvent,
            queryStringParameters: {
              status: 2,
            },
            multiValueQueryStringParameters: {},
          },
        },
      ];

      for (const testCase of testCases) {
        const event = {
          ...mockEvent,
          ...testCase.event,
          queryStringParameters: {
            ...mockEvent.queryStringParameters,
            ...Object.fromEntries(
              Object.entries(testCase.event.queryStringParameters || {}).map(
                ([k, v]) => [k, String(v)],
              ),
            ),
          },
          multiValueQueryStringParameters:
            testCase.event.multiValueQueryStringParameters || {},
        };

        const result = await getTemplateListHandler(
          event as LambdaEvent,
          mockContext,
        );
        const response = JSON.parse(result.body);

        expect(result.statusCode, `Failed for case: ${testCase.name}`).to.equal(
          StatusCodes.OK,
        );
        expect(
          response.result.data,
          `Failed for case: ${testCase.name}`,
        ).to.be.an('array');
        expect(
          response.result.pagination,
          `Failed for case: ${testCase.name}`,
        ).to.include.keys('totalItems', 'totalPages', 'page', 'pageSize');
        expect(
          response.result.userDetails,
          `Failed for case: ${testCase.name}`,
        ).to.be.an('array');
      }

      // Verify search functionality
      expect(categoryStub.called, 'Category search not performed').to.be.true;
      expect(hazardStub.called, 'Hazard search not performed').to.be.true;

      // Verify user details integration
      expect(vesselServiceStub.called, 'User details not fetched').to.be.true;
    });
    it('should handle various validation errors properly', async () => {
      const validationTestCases = [
        {
          name: 'invalid sort field',
          event: {
            ...mockEvent,
            queryStringParameters: {
              sort_by: 'invalid_field',
              sort_order: 'ASC',
            },
          },
          expectedError: 'Invalid sort field',
        },
        {
          name: 'invalid sort order',
          event: {
            ...mockEvent,
            queryStringParameters: {
              sort_by: 'created_at',
              sort_order: 'INVALID',
            },
          },
          expectedError: 'Invalid sort order',
        },
      ];

      for (const testCase of validationTestCases) {
        // Reset stubs for each test case
        sinon.restore();

        // Setup validation stub for error case
        const validationStub = sinon
          .stub(TemplateDto, 'getTemplateListSchema')
          .returns({
            validate: () => ({
              error: {
                details: [{message: testCase.expectedError}],
              },
            }),
          });

        const result = await getTemplateListHandler(
          testCase.event as LambdaEvent,
          mockContext,
        );

        expect(result.statusCode, `Failed for case: ${testCase.name}`).to.equal(
          StatusCodes.BAD_REQUEST,
        );
      }
    });

    it('should handle database errors gracefully', async () => {
      sinon.restore();
      sinon
        .stub(Template, 'findAndCountAll')
        .rejects(new Error('Database error'));

      const result = await getTemplateListHandler(mockEvent, mockContext);
      expect(result.statusCode).to.equal(StatusCodes.INTERNAL_SERVER_ERROR);
    });

    it('should throw DB error for listing', async () => {
      sinon.restore();
      sinon.stub(Category, 'findAll').resolves([]);
      sinon.stub(Hazard, 'findAll').resolves([]);
      sinon.stub(Template, 'findAll').resolves([]);
      sinon
        .stub(Template, 'findAndCountAll')
        .rejects(new Error('Database Error'));

      const event = {
        queryStringParameters: {search: 'test'},
        multiValueQueryStringParameters: {},
      };

      const result = await getTemplateListHandler(event as any, {} as any);
      expect(result.statusCode).to.equal(StatusCodes.INTERNAL_SERVER_ERROR);
    });

    it('should return validation error 400', async () => {
      const event = {
        queryStringParameters: {search: ''},
        multiValueQueryStringParameters: {},
      };

      const result = await getTemplateListHandler(event as any, {} as any);
      expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
    });
  });

  describe('parseQueryParams', () => {
    it('should return empty object for empty inputs', () => {
      const result = parseQueryParams({}, {});
      expect(result).to.deep.equal({});
    });

    it('should parse flat query parameters', () => {
      const queryParams = {search: 'test', sortBy: 'name'};
      const result = parseQueryParams(queryParams, {});
      expect(result).to.deep.equal({search: 'test', sortBy: 'name'});
    });

    it('should parse nested query parameters like createdOn[startDate]', () => {
      const queryParams = {
        'createdOn[startDate]': '2024-01-01',
        'createdOn[endDate]': '2024-01-31',
      };
      const result = parseQueryParams(queryParams, {});
      expect(result).to.deep.equal({
        createdOn: {
          startDate: '2024-01-01',
          endDate: '2024-01-31',
        },
      });
    });

    it('should convert numeric string arrays to numbers for multi-value params', () => {
      const multiValueParams = {
        'ra_categories[]': ['3', '4'],
      };

      const result = parseQueryParams({}, multiValueParams);
      expect(result).to.deep.equal({
        ra_categories: ['3', '4'],
      });
    });

    it('should preserve non-numeric strings in multi-value params', () => {
      const multiValueParams = {
        'hazard_categories[]': ['flammable', 'explosive', '123'],
      };
      const result = parseQueryParams({}, multiValueParams);
      expect(result).to.deep.equal({
        hazard_categories: ['flammable', 'explosive', '123'],
      });
    });

    it('should merge query string and multi-value params correctly', () => {
      const queryParams = {
        search: 'test',
        'created_on[start_date]': '2024-01-01',
      };
      const multiValueParams = {'ra_categories[]': ['10', '20']};
      const result = parseQueryParams(queryParams, multiValueParams);
      expect(result).to.deep.equal({
        search: 'test',
        created_on: {
          start_date: '2024-01-01',
        },
        ra_categories: ['10', '20'],
      });
    });

    it('should handle nested and array fields', () => {
      const queryParams = {
        'created_on[start_date]': '2024-01-01',
        'created_on[end_date]': '2024-01-31',
        search: 'test',
      };
      const multiValueParams = {
        'ra_categories[]': ['1', '2'],
        'hazard_categories[]': ['3', '4'],
      };
      const result = parseQueryParams(queryParams, multiValueParams);
      expect(result).to.deep.equal({
        created_on: {start_date: '2024-01-01', end_date: '2024-01-31'},
        search: 'test',
        ra_categories: ['1', '2'],
        hazard_categories: ['3', '4'],
      });
    });
  });

  // --- Add coverage for missing lines/branches in template.ts ---

  it('should create template with "DRAFT" status if not published', async () => {
    mockEvent.body = JSON.stringify({...templateInput, status: 'DRAFT'});
    sinon.stub(TemplateValidation, 'validateTemplate').returns([]);
    const createTemplateStub = sinon
      .stub(Template, 'create')
      .resolves({id: 999, ...templateInput} as any);
    sinon.stub(TemplateKeyword, 'create').resolves();
    sinon.stub(TemplateCategory, 'create').resolves();
    sinon.stub(Hazard, 'create').resolves({id: 20} as any);
    sinon.stub(TemplateHazard, 'create').resolves();
    sinon.stub(Parameter, 'create').resolves({id: 20} as any);
    sinon.stub(TemplateParameter, 'create').resolves();
    sinon.stub(TemplateJob, 'create').resolves({id: 1} as any);
    sinon.stub(TemplateJobInitialRiskRating, 'create').resolves();
    sinon.stub(TemplateJobResidualRiskRating, 'create').resolves();
    sinon.stub(TemplateTaskReliabilityAssessment, 'create').resolves();
    const result = await createTemplateHandler(mockEvent, mockContext);
    expect(createTemplateStub.calledOnce).to.be.true;
    expect(result.statusCode).to.equal(StatusCodes.CREATED);
  });

  it('should handle template_hazard with is_other false', async () => {
    mockEvent.body = JSON.stringify({
      ...templateInput,
      template_hazard: {is_other: false, hazard_id: [10], value: 'Hazard'},
    });
    sinon.stub(TemplateValidation, 'validateTemplate').returns([]);
    sinon.stub(Template, 'create').resolves({id: 999, ...templateInput} as any);
    sinon.stub(TemplateKeyword, 'create').resolves();
    sinon.stub(TemplateCategory, 'create').resolves();
    sinon.stub(TemplateHazard, 'create').resolves();
    sinon.stub(Parameter, 'create').resolves({id: 20} as any);
    sinon.stub(TemplateParameter, 'create').resolves();
    sinon.stub(TemplateJob, 'create').resolves({id: 1} as any);
    sinon.stub(TemplateJobInitialRiskRating, 'create').resolves();
    sinon.stub(TemplateJobResidualRiskRating, 'create').resolves();
    sinon.stub(TemplateTaskReliabilityAssessment, 'create').resolves();
    const result = await createTemplateHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.CREATED);
  });

  it('should mark a template and related tables as inactive and return 200', async () => {
    const templateId = 100;
    mockEvent.pathParameters = {id: String(templateId)};
    mockEvent.httpMethod = 'PATCH';
    mockEvent.path = `/templates/${templateId}/inactive`;

    const fakeTemplateInstance = {
      id: templateId,
      update: sinon.stub().resolves(),
    };

    sinon.stub(Template, 'findByPk').resolves(fakeTemplateInstance as any);
    sinon.stub(TemplateCategory, 'update').resolves();
    sinon.stub(TemplateKeyword, 'update').resolves();
    sinon.stub(TemplateHazard, 'update').resolves();
    sinon.stub(TemplateTaskReliabilityAssessment, 'update').resolves();
    sinon.stub(TemplateJob, 'update').resolves();
    const {markTemplateInactiveHandler} = require('../../handlers/template');
    const result = await markTemplateInactiveHandler(mockEvent, mockContext);

    expect(result.statusCode).to.equal(StatusCodes.OK);
    expect(transaction.commit.calledOnce).to.be.true;
  });

  it('should return 400 if template id is missing for markTemplateInactiveHandler', async () => {
    mockEvent.pathParameters = undefined;
    const {markTemplateInactiveHandler} = require('../../handlers/template');
    const result = await markTemplateInactiveHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.BAD_REQUEST);
  });

  it('should return 404 if template not found for markTemplateInactiveHandler', async () => {
    mockEvent.pathParameters = {id: '999'};
    sinon.stub(Template, 'findByPk').resolves(undefined);
    const {markTemplateInactiveHandler} = require('../../handlers/template');
    const result = await markTemplateInactiveHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.NOT_FOUND);
  });

  it('should return 500 and rollback on DB error in markTemplateInactiveHandler', async () => {
    mockEvent.pathParameters = {id: '100'};
    sinon.stub(Template, 'findByPk').throws(new Error('DB Error'));
    const {markTemplateInactiveHandler} = require('../../handlers/template');
    const result = await markTemplateInactiveHandler(mockEvent, mockContext);
    expect(result.statusCode).to.equal(StatusCodes.INTERNAL_SERVER_ERROR);
  });

  describe('getTemplateUsersHandler', () => {
    let mockToken: string;

    beforeEach(() => {
      sinon.restore();
      mockToken = 'mockAccessToken';
      sinon.stub(VesselMasterDataService, 'getAccessToken').resolves(mockToken);
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return unique users and their details', async () => {
      const mockUsers = [{created_by: 'user1'}, {created_by: 'user2'}];

      const mockUserDetails = [
        {
          id: 'user1',
          email: '<EMAIL>',
        },
        {
          id: 'user2',
          email: '<EMAIL>',
        },
      ];

      const axiosStub = sinon.stub(axios, 'request');
      axiosStub
        .withArgs(
          sinon.match({url: sinon.match(/\/auth\/v1\/identity\/token/)}),
        )
        .resolves({data: {access_token: mockToken}});
      axiosStub
        .withArgs(sinon.match({url: sinon.match(/\/users\?/)}))
        .resolves({data: mockUserDetails});

      sinon.stub(Template, 'findAll').resolves(mockUsers as any);

      const result = await getTemplateUsersHandler(mockEvent, mockContext);
      const response = JSON.parse(result.body);

      expect(result.statusCode).to.equal(StatusCodes.OK);
      expect(response.message).to.equal('Template users');
    });

    it('should return empty array when no users found', async () => {
      const axiosStub = sinon.stub(axios, 'request');
      axiosStub
        .withArgs(
          sinon.match({url: sinon.match(/\/auth\/v1\/identity\/token/)}),
        )
        .resolves({data: {access_token: mockToken}});

      const mockFind = sinon.stub(Template, 'findAll').resolves([]);

      const result = await getTemplateUsersHandler(mockEvent, mockContext);
      const response = JSON.parse(result.body);

      expect(result.statusCode).to.equal(StatusCodes.OK);
      expect(response.result).to.deep.equal([]);
    });

    it('should handle VesselMasterDataService error gracefully', async () => {
      const mockUsers = [{created_by: 'user1'}, {created_by: 'user2'}];

      const axiosStub = sinon.stub(axios, 'request');
      axiosStub
        .withArgs(
          sinon.match({url: sinon.match(/\/auth\/v1\/identity\/token/)}),
        )
        .resolves({data: {access_token: mockToken}});
      axiosStub
        .withArgs(sinon.match({url: sinon.match(/\/users\?/)}))
        .rejects(new Error('API Error'));

      sinon.stub(Template, 'findAll').resolves(mockUsers as any);

      const result = await getTemplateUsersHandler(mockEvent, mockContext);
      const response = JSON.parse(result.body);

      expect(result.statusCode).to.equal(StatusCodes.OK);
      expect(response.result).to.deep.equal([]);
    });

    it('should return 500 on database error', async () => {
      const mockVesselMasterDataService = {
        getUserDetails: sinon.stub(),
      };

      sinon
        .stub(require('../../controller/vessel-api-controller'), 'default')
        .returns(mockVesselMasterDataService);

      const mockFind = sinon
        .stub(Template, 'findAll')
        .rejects(new Error('Database Error'));

      const result = await getTemplateUsersHandler(mockEvent, mockContext);

      expect(mockFind.called).to.be.true;
      expect(mockVesselMasterDataService.getUserDetails.called).to.be.false;
      expect(result.statusCode).to.equal(StatusCodes.INTERNAL_SERVER_ERROR);
    });
  });
});
