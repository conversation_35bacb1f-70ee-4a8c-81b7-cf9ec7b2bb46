import {Transaction, Op, fn, col, literal} from 'sequelize';

interface UserDetails {
  userId: string;
  email: string;
  full_name?: string;
}

interface UserResponse {
  users: Array<{
    id: string;
    email: string;
    username: string;
    first_name: string;
    last_name: string | null;
    full_name: string;
    attributes: Record<string, unknown>;
  }>;
}

interface TopTemplate {
  template_id: number;
  template_count: string | number;
}

import {StatusCodes, RiskTemplateStatus, BasicStatus} from '../enums';
import {
  handleError,
  handleValidationError,
  handleNotFoundError,
  CustomHttpError,
} from '../error';
import {sq, RISK_DB} from '../db/db-client';
import {
  Category,
  Hazard,
  Template,
  TemplateCategory,
  TemplateHazard,
  TemplateJob,
  TemplateJobInitialRiskRating,
  TemplateJobResidualRiskRating,
  TemplateParameter,
  TemplateTaskReliabilityAssessment,
  ActivityLog,
  Risk,
} from '../models';
import {EntityType, ActionType} from '../models/activity-log.model';
import {ITemplateCreate, ITemplateAttributes} from '../types/template.types';
import {validateTemplate} from '../validation/template.validation';
import {createLambda} from '../utils/lambda';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {getTemplateListSchema} from '../dtos/template.dto';
import TemplateKeyword from '../models/template-keyword.model';
import Paris2Controller from '../controller/paris2-api-controller';
import qs from 'qs';

const createTemplate = async (
  template: ITemplateCreate,
  transaction: Transaction,
  userId: string,
  templateId?: number,
): Promise<ITemplateAttributes | ITemplateCreate> => {
  let tempId = templateId;
  let templateRecord;

  if (!templateId) {
    templateRecord = await Template.create(
      {
        task_requiring_ra: template.task_requiring_ra,
        task_duration: template.task_duration,
        task_alternative_consideration: template.task_alternative_consideration,
        task_rejection_reason: template.task_rejection_reason,
        worst_case_scenario: template.worst_case_scenario,
        recovery_measures: template.recovery_measures,
        status:
          template.status === 'PUBLISHED'
            ? RiskTemplateStatus.PUBLISHED
            : RiskTemplateStatus.DRAFT,
        created_by: userId,
        publish_on: template.status === 'PUBLISHED' ? new Date() : null,
        draft_step: template.draft_step,
      } as ITemplateAttributes,
      {transaction},
    );
    tempId = templateRecord.id;

    for (const keyword of template.template_keyword ?? []) {
      await TemplateKeyword.create(
        {
          template_id: tempId,
          name: keyword,
          created_by: userId,
          status: BasicStatus.ACTIVE,
        },
        {transaction},
      );
    }

    // Log template creation
    await ActivityLog.create(
      {
        entity_id: tempId,
        entity_type: EntityType.TEMPLATE,
        action_type: ActionType.CREATE,
        parameter: templateRecord.toJSON(),
        created_by: userId,
      },
      {transaction},
    );
  }
  await Promise.all([
    handleKeywords(template, tempId!, transaction, userId),
    handleCategories(template, tempId!, transaction, userId),
    handleHazards(template, tempId!, transaction, userId),
    handleParameters(template, tempId!, transaction, userId),
    handleJobs(template, tempId!, transaction, userId),
    handleAssessments(template, tempId!, transaction, userId),
  ]);
  return templateId ? template : (templateRecord as ITemplateAttributes);
};

const handleKeywords = async (
  template: ITemplateCreate,
  tempId: number,
  transaction: Transaction,
  userId?: string,
) => {
  for (const keyword of template.template_keyword ?? []) {
    await TemplateKeyword.create(
      {
        template_id: tempId,
        name: keyword,
        created_by: userId,
        status: BasicStatus.ACTIVE,
      },
      {transaction},
    );
  }
};

// POST /template
/**
 * @swagger
 * /templates:
 *   post:
 *     tags:
 *       - Template
 *     summary: Create a new template
 *     description: Creates a new template with the provided details.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               task_requiring_ra:
 *                 type: string
 *                 description: Task requiring risk assessment
 *               task_duration:
 *                 type: integer
 *                 description: Duration of the task
 *               task_duration_unit:
 *                 type: string
 *                 description: Unit of task duration
 *               template_category:
 *                 type: object
 *                 properties:
 *                   category_id:
 *                     type: array
 *                     items:
 *                       type: integer
 *                 description: Categories associated with the template
 *               template_hazard:
 *                 type: object
 *                 properties:
 *                   hazard_id:
 *                     type: array
 *                     items:
 *                       type: integer
 *                   is_other:
 *                     type: boolean
 *                   value:
 *                     type: string
 *                 description: Hazards associated with the template
 *               parameters:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     parameter_id:
 *                       type: array
 *                       items:
 *                         type: integer
 *                     parameter_type_id:
 *                       type: integer
 *                     is_other:
 *                       type: boolean
 *                     value:
 *                       type: string
 *                 description: Parameters associated with the template
 *     responses:
 *       201:
 *         description: Template created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Template'
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
export const createTemplateHandler = createLambda(
  async ({jsonBody, user}: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sq.transaction();
    try {
      const template = jsonBody as ITemplateCreate;
      const isPublish = template.status === 'PUBLISHED';
      const validationErrors = validateTemplate(template, isPublish);
      if (validationErrors.length > 0) {
        return handleValidationError(validationErrors.join(', '));
      }
      const templateRecord = await createTemplate(
        template,
        transaction,
        user?.user_id,
      );
      await transaction.commit();
      return {
        statusCode: StatusCodes.CREATED,
        response: templateRecord,
      };
    } catch (error) {
      await transaction.rollback();
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// PATCH /template/{id}
/**
 * @swagger
 * /templates/{id}:
 *   patch:
 *     tags:
 *       - Template
 *     summary: Update an existing template
 *     description: Updates an existing template with the provided details.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the template to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Template'
 *     responses:
 *       200:
 *         description: Template updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Template not found
 *       500:
 *         description: Internal server error
 */
export const patchTemplateHandler = createLambda(
  async ({
    jsonBody,
    pathParameters,
    user,
  }: LambdaData): Promise<LambdaResponse> => {
    console.log("Inside patch template handler function")
    const transaction = await sq.transaction();
    try {
      const template = jsonBody as ITemplateCreate;
      const templateId = pathParameters?.id;
      const userId = String(user?.user_id);

      if (!templateId) {
        return handleValidationError('Template ID is required');
      }

      const existingTemplate = await Template.findByPk(templateId);
      if (!existingTemplate) {
        return handleValidationError('Template not found');
      }

      // Log template update with current state before updating
      await ActivityLog.create(
        {
          entity_id: Number(templateId),
          entity_type: EntityType.TEMPLATE,
          action_type: ActionType.UPDATE,
          parameter: existingTemplate.toJSON(),
          created_by: userId,
        },
        {transaction},
      );

      const isPublish = template.status === 'PUBLISHED';
      const validationErrors = validateTemplate(template, isPublish);
      if (validationErrors.length > 0) {
        return handleValidationError(validationErrors.join(', '));
      }

      // Delete existing related records
      await TemplateCategory.destroy({
        where: {template_id: templateId},
        transaction,
      });

      await TemplateKeyword.destroy({
        where: {template_id: templateId},
        transaction,
      });
      await TemplateHazard.destroy({
        where: {template_id: templateId},
        transaction,
      });
      await TemplateTaskReliabilityAssessment.destroy({
        where: {template_id: templateId},
        transaction,
      });

      const jobs = await TemplateJob.findAll({
        where: {template_id: templateId},
      });
      for (const job of jobs) {
        await TemplateJobInitialRiskRating.destroy({
          where: {template_job_id: job.id},
          transaction,
        });
        await TemplateJobResidualRiskRating.destroy({
          where: {template_job_id: job.id},
          transaction,
        });
      }
      await TemplateJob.destroy({
        where: {template_id: templateId},
        transaction,
      });

      // Update template
      const updatedFields: any = {
        task_requiring_ra: template.task_requiring_ra,
        task_duration: template.task_duration,
        task_alternative_consideration: template.task_alternative_consideration,
        task_rejection_reason: template.task_rejection_reason,
        worst_case_scenario: template.worst_case_scenario,
        recovery_measures: template.recovery_measures,
        status:
          template.status === 'PUBLISHED'
            ? RiskTemplateStatus.PUBLISHED
            : RiskTemplateStatus.DRAFT,
        updated_by: user?.user_id,
        draft_step: template.draft_step,
      };
      if (template.status === 'PUBLISHED') {
        updatedFields.publish_on = new Date();
      }
      await existingTemplate.update(updatedFields, {transaction});
      // Create new records
      await createTemplate(
        template,
        transaction,
        user?.user_id,
        existingTemplate.id,
      );
      await transaction.commit();

      return {
        statusCode: StatusCodes.OK,
        response: {
          message: 'Template updated successfully',
          result: existingTemplate,
        },
      };
    } catch (error) {
      await transaction.rollback();
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);
// PATCH /template/{id}/inactive
/**
 * @swagger
 * /templates/{id}/inactive:
 *   patch:
 *     tags:
 *       - Template
 *     summary: Mark a template and related tables as inactive
 *     description: Updates the status of a template and its related tables to inactive.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the template to mark as inactive
 *     responses:
 *       200:
 *         description: Template marked as inactive successfully
 *       404:
 *         description: Template not found
 *       500:
 *         description: Internal server error
 */
export const markTemplateInactiveHandler = createLambda(
  async ({pathParameters, user}: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sq.transaction();
    try {
      const templateId = pathParameters?.id;

      if (!templateId) {
        return handleValidationError('Template ID is required');
      }

      const existingTemplate = await Template.findByPk(templateId);
      if (!existingTemplate) {
        return handleNotFoundError('Template not found');
      }

      // Update template and related tables to inactive
      await Promise.all([
        existingTemplate.update(
          {status: RiskTemplateStatus.INACTIVE, updated_by: user?.user_id},
          {transaction},
        ),
        TemplateCategory.update(
          {status: BasicStatus.INACTIVE},
          {where: {template_id: templateId}, transaction},
        ),
        TemplateKeyword.update(
          {status: BasicStatus.INACTIVE},
          {where: {template_id: templateId}, transaction},
        ),
        TemplateHazard.update(
          {status: BasicStatus.INACTIVE},
          {where: {template_id: templateId}, transaction},
        ),
        TemplateTaskReliabilityAssessment.update(
          {status: BasicStatus.INACTIVE},
          {where: {template_id: templateId}, transaction},
        ),
        TemplateJob.update(
          {status: BasicStatus.INACTIVE},
          {where: {template_id: templateId}, transaction},
        ),
      ]);

      await transaction.commit();

      return {
        statusCode: StatusCodes.OK,
        response: {
          message:
            'Template and related tables marked as inactive successfully',
        },
      };
    } catch (error) {
      await transaction.rollback();
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// GET /template/{id}
/**
 * @swagger
 * /templates/{id}:
 *   get:
 *     tags:
 *       - Template
 *     summary: Get a template by ID
 *     description: Retrieves a template by its ID.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the template to retrieve
 *     responses:
 *       200:
 *         description: Template retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Template'
 *       404:
 *         description: Template not found
 *       500:
 *         description: Internal server error
 */
export const getTemplateByIdHandler = createLambda(
  async ({pathParameters}: LambdaData): Promise<LambdaResponse> => {
    try {
      const templateId = pathParameters?.id;

      if (!templateId) {
        return handleValidationError('Template ID is required');
      }

      const template = await Template.findByPk(templateId, {
        include: [
          {
            association: 'template_category',
            attributes: ['id'],
            include: [{association: 'category', attributes: ['name', 'id']}],
          },
          {
            association: 'template_hazards',
            attributes: ['id', 'hazard_category_is_other', 'value'],
            include: [
              {association: 'hazard_detail', attributes: ['name', 'id']},
            ],
          },
          {
            association: 'template_parameter',
            attributes: ['id', 'parameter_is_other', 'value'],
            include: [
              {association: 'parameterType', attributes: ['name', 'id']},
              {association: 'parameter', attributes: ['name', 'id']},
            ],
          },
          {
            association: 'template_job',
            attributes: [
              'id',
              'job_step',
              'job_hazard',
              'job_nature_of_risk',
              'job_existing_control',
              'job_additional_mitigation',
            ],
            include: [
              {
                association: 'template_job_initial_risk_rating',
                attributes: [
                  'id',
                  'template_job_id',
                  'parameter_type_id',
                  'rating',
                ],
              },
              {
                association: 'template_job_residual_risk_rating',
                attributes: [
                  'id',
                  'template_job_id',
                  'parameter_type_id',
                  'rating',
                  'reason',
                ],
                separate: true,
              },
            ],
          },
          {
            association: 'template_task_reliability_assessment',
            attributes: [
              'id',
              'condition',
              'task_reliability_assessment_id',
              'task_reliability_assessment_answer',
            ],
            separate: true,
          },
          {association: 'template_keywords', attributes: ['id', 'name']},
        ],

        logging: console.log,
      });

      if (!template) {
        return handleNotFoundError('Template not found');
      }

      return {
        statusCode: StatusCodes.OK,
        response: {message: 'Template', result: template},
      };
    } catch (error) {
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// GET /template/users
/**
 * @swagger
 * /templates/users:
 *   get:
 *     tags:
 *       - Template
 *     summary: Get all unique users who created templates
 *     description: Retrieves all unique user IDs from template creators and their details
 *     responses:
 *       200:
 *         description: List of unique users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 result:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       email:
 *                         type: string
 *       500:
 *         description: Internal server error
 */
export const getTemplateUsersHandler = createLambda(
  async (): Promise<LambdaResponse> => {
    try {
      // Get unique created_by values from Template table
      const uniqueUsers = await Template.findAll({
        attributes: [[fn('DISTINCT', col('created_by')), 'created_by']],
        where: literal("created_by IS NOT NULL AND created_by != ''"),
        raw: true,
      });

      const userIds = uniqueUsers
        .map(user => user.created_by)
        .filter(Boolean) as string[];

      const userDetails: UserDetails[] = await fetchUserDetails(userIds);

      return {
        statusCode: StatusCodes.OK,
        response: {
          message: 'Template users',
          result: userDetails,
        },
      };
    } catch (error) {
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// GET /template
/**
 * @swagger
 * /templates:
 *   get:
 *     tags:
 *       - Template
 *     summary: Get a list of templates
 *     description: Retrieves a list of templates with optional filters.
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter templates
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sort_order
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order
 *       - in: query
 *         name: created_by
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         style: form
 *         explode: true
 *         description: Filter by creator IDs
 *       - in: query
 *         name: created_by[start_date]
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by start date of creation
 *       - in: query
 *         name: created_by[end_date]
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by end date of creation
 *       - in: query
 *         name: ra_categories
 *         schema:
 *           type: array
 *           items:
 *             type: integer
 *         style: form
 *         explode: true
 *         description: Filter by risk assessment category IDs
 *       - in: query
 *         name: hazard_categories
 *         schema:
 *           type: array
 *           items:
 *             type: integer
 *         style: form
 *         explode: true
 *         description: Filter by hazard category IDs
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of templates retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Template'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *       500:
 *         description: Internal server error
 */
export const getTemplateListHandler = createLambda(
  async ({
    multiValueQueryStringParameters,
    queryStringParameters,
  }: LambdaData): Promise<LambdaResponse> => {
    try {
      // Parse and validate query parameters
      const formattedQuery = parseQueryParams(
        queryStringParameters,
        multiValueQueryStringParameters,
      );
      const {error, value} = getTemplateListSchema.validate(formattedQuery);
      if (error) {
        return handleValidationError(error.message);
      }

      const {
        search,
        created_by,
        created_at,
        ra_categories,
        hazard_categories,
        sort_by,
        sort_order,
        page = 1,
        limit = 10,
        status,
      } = value;
      const offset = (page - 1) * limit;
      const sortField = sort_by ?? 'created_at';
      const sortDirection = sort_order ?? 'ASC';
      //-----In case of draft send data without adding any filter---------//
      if (status === RiskTemplateStatus.DRAFT) {
        const draftResult = await Template.findAndCountAll({
          where: {status: RiskTemplateStatus.DRAFT},
          attributes: ['id', 'task_requiring_ra', 'updated_at', 'created_at'],
          limit,
          offset,
          order: [[sortField, sortDirection]],
        });

        const totalItems = draftResult.count;
        const totalPages = Math.ceil(totalItems / limit);
        return {
          statusCode: StatusCodes.OK,
          response: {
            message: 'Template Draft list',
            result: {
              data: draftResult.rows,
              pagination: {
                totalItems,
                totalPages,
                page,
                pageSize: limit,
              },
            },
          },
        };
      }

      // Build where clause
      const whereClause: any = {};
      if (search?.trim()) {
        const trimmedSearch = search.trim();

        // Fetch category IDs for search
        const categoryIds = (
          await Category.findAll({
            where: {name: {[Op.iLike]: `%${trimmedSearch}%`}},
            attributes: ['id'],
            raw: true,
          })
        ).map(cat => cat.id);

        // Fetch hazard IDs for search
        const hazardIds = (
          await Hazard.findAll({
            where: {name: {[Op.iLike]: `%${trimmedSearch}%`}},
            attributes: ['id'],
            raw: true,
          })
        ).map(haz => haz.id);

        whereClause[Op.or] = [
          {task_requiring_ra: {[Op.iLike]: `%${trimmedSearch}%`}},
          ...categoryIds.map(id => ({'$template_category.category_id$': id})),
          {
            [Op.or]: [
              ...hazardIds.map(id => ({'$template_hazards.hazard_id$': id})),
              {'$template_hazards.value$': {[Op.iLike]: `%${trimmedSearch}%`}},
            ],
          },
          {'$template_keywords.name$': {[Op.iLike]: `%${trimmedSearch}%`}},
        ];
      }

      if (created_by) {
        whereClause.created_by = {[Op.in]: created_by};
      }
      if (status) {
        whereClause.status = {[Op.eq]: status};
      }

      if (created_at?.start_date) {
        const startDate = new Date(created_at.start_date);
        startDate.setHours(0, 0, 0, 0);
        whereClause.created_at = {[Op.gte]: startDate};
      }

      if (created_at?.end_date) {
        const endDate = new Date(created_at.end_date);
        endDate.setHours(23, 59, 59, 999);
        whereClause.created_at = {
          ...whereClause.created_at,
          [Op.lte]: endDate,
        };
      }
      const shouldIncludeHazard =
        (hazard_categories && hazard_categories.length > 0) ||
        (search && search.trim() !== '');

      const hazardInclude = shouldIncludeHazard
        ? [
            {
              model: TemplateHazard,
              as: 'template_hazards',
              required: !!hazard_categories?.length,
              attributes: [],
              where: hazard_categories?.length
                ? {
                    hazard_id: {[Op.in]: hazard_categories},
                    status: BasicStatus.ACTIVE,
                  }
                : {
                    status: BasicStatus.ACTIVE,
                  },
            },
          ]
        : [];
      // Step 1: Get all matching template IDs (no pagination here)
      const allMatchingTemplates = await Template.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: TemplateKeyword,
            as: 'template_keywords',
            required: false,
            attributes: [],
            where: {
              status: BasicStatus.ACTIVE,
            },
          },
          ...hazardInclude,
          ...(ra_categories?.length
            ? [
                {
                  model: TemplateCategory,
                  as: 'template_category',
                  required: true,
                  attributes: [],
                  where: {
                    category_id: {[Op.in]: ra_categories},
                    status: BasicStatus.ACTIVE,
                  },
                },
              ]
            : []),
        ],
        attributes: ['id'],
        distinct: true,
        subQuery: false,
        raw: false,
      });

      const totalItems = allMatchingTemplates.count;
      const totalPages = Math.ceil(totalItems / limit);
      const paginatedIds = allMatchingTemplates.rows
        .slice(offset, offset + limit)
        .map(t => t.id);
      const rows = await Template.findAll({
        attributes: ['id', 'task_requiring_ra', 'created_by', 'created_at'],
        where: {id: {[Op.in]: paginatedIds}},
        include: [
          {
            model: TemplateCategory,
            as: 'template_category',
            required: false,
            where: {status: BasicStatus.ACTIVE},
            attributes: ['id', 'category_id'],
            include: [
              {
                model: Category,
                as: 'category',
                attributes: ['id', 'name'],
              },
            ],
          },
          {
            model: TemplateHazard,
            as: 'template_hazards',
            required: false,
            where: {status: BasicStatus.ACTIVE},
            attributes: ['id', 'hazard_category_is_other', 'value'],
            include: [
              {
                model: Hazard,
                as: 'hazard_detail',
                attributes: ['id', 'name'],
              },
            ],
          },
          {
            model: TemplateKeyword,
            as: 'template_keywords',
            required: false,
            attributes: ['id', 'name'],
          },
        ],
        order: [[sortField, sortDirection]],
      });

      // Prepare response
      const userIds = Array.from(
        new Set(rows.map(row => row.created_by)),
      ).filter(Boolean) as string[];

      const userDetails: UserDetails[] = await fetchUserDetails(userIds);

      return {
        statusCode: StatusCodes.OK,
        response: {
          message: 'Template list',
          result: {
            data: rows,
            pagination: {
              totalItems,
              totalPages,
              page,
              pageSize: limit,
            },
            userDetails,
          },
        },
      };
    } catch (error) {
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

export function parseQueryParams(
  queryStringParams: {[key: string]: string} = {},
  multiValueParams: {[key: string]: string[]} | null = null,
): Record<string, any> {
  const params: Record<string, any> = {...queryStringParams};
  const arrayFields = [
    'created_by[]',
    'ra_categories[]',
    'hazard_categories[]',
  ];
  if (multiValueParams) {
    arrayFields.forEach(field => {
      if (multiValueParams[field]) {
        params[field] = multiValueParams[field];
      }
    });
  } else {
    arrayFields.forEach(field => {
      if (!params[field]) {
        params[field] = [];
      }
    });
  }
  const queryString = qs.stringify(params, {arrayFormat: 'repeat'});
  const result = qs.parse(queryString, {allowDots: true, comma: false});
  for (const field of arrayFields) {
    if (Array.isArray(result[field])) {
      result[field] = result[field].map((val: any) =>
        isNaN(+val) ? val : Number(val),
      );
    }
  }

  return result;
}

// DELETE /template/{id}
/**
 * @swagger
 * /templates/{id}:
 *   delete:
 *     tags:
 *       - Template
 *     summary: Delete a template
 *     description: Deletes a template by its ID.

 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the template to delete
 *     responses:
 *       200:
 *         description: Template deleted successfully
 *       404:
 *         description: Template not found
 *       500:
 *         description: Internal server error
 */
export const deleteTemplateHandler = createLambda(
  async ({pathParameters, user}: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sq.transaction();
    try {
      const templateId = pathParameters?.id;
      const userId = String(user?.user_id);

      if (!templateId) {
        return handleValidationError('Template ID is required');
      }

      const existingTemplate = await Template.findByPk(templateId);
      if (existingTemplate?.status !== RiskTemplateStatus.DRAFT) {
        return handleValidationError(
          'Only templates with status DRAFT can be deleted',
        );
      }
      if (!existingTemplate) {
        return handleNotFoundError('Template not found');
      }

      // Log template deletion (no need to store parameter data)
      await ActivityLog.create(
        {
          entity_id: Number(templateId),
          entity_type: EntityType.TEMPLATE,
          action_type: ActionType.DELETE,
          parameter: null,
          created_by: userId,
        },
        {transaction},
      );

      // Delete all associated records
      const [jobs] = await Promise.all([
        TemplateJob.findAll({
          where: {template_id: templateId},
          transaction,
        }),
        TemplateCategory.destroy({
          where: {template_id: templateId},
          transaction,
        }),
        TemplateKeyword.destroy({
          where: {template_id: templateId},
          transaction,
        }),
        TemplateHazard.destroy({where: {template_id: templateId}, transaction}),
        TemplateParameter.destroy({
          where: {template_id: templateId},
          transaction,
        }),
        TemplateKeyword.destroy({
          where: {template_id: templateId},
          transaction,
        }),
        TemplateTaskReliabilityAssessment.destroy({
          where: {template_id: templateId},
          transaction,
        }),
      ]);

      // Delete job ratings and jobs
      const jobIds = jobs.map(job => job.id);
      await Promise.all([
        TemplateJobInitialRiskRating.destroy({
          where: {template_job_id: jobIds},
          transaction,
        }),
        TemplateJobResidualRiskRating.destroy({
          where: {template_job_id: jobIds},
          transaction,
        }),
      ]);
      await TemplateJob.destroy({
        where: {template_id: templateId},
        transaction,
      });

      // Delete the template
      await Template.destroy({where: {id: templateId}, transaction});

      await transaction.commit();

      return {
        statusCode: StatusCodes.OK,
        response: {message: 'Template deleted successfully', result: true},
      };
    } catch (error) {
      await transaction.rollback();
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// Helper function to handle categories
const handleCategories = async (
  template: ITemplateCreate,
  tempId: number,
  transaction: Transaction,
  userId?: string,
) => {
  for (const categoryId of template.template_category?.category_id ?? []) {
    await TemplateCategory.create(
      {
        template_id: tempId,
        category_id: categoryId,
        category_is_other: false,
        status: BasicStatus.ACTIVE,
        created_by: userId,
      },
      {transaction},
    );
  }
};

// Helper function to handle hazards
const handleHazards = async (
  template: ITemplateCreate,
  tempId: number,
  transaction: Transaction,
  userId?: string,
) => {
  if (template.template_hazard?.is_other) {
    await TemplateHazard.create(
      {
        template_id: tempId,
        hazard_category_is_other: true,
        created_by: userId,
        value: template.template_hazard?.value ?? '',
      },
      {transaction},
    );
  }
  for (const hazardId of template.template_hazard?.hazard_id ?? []) {
    await TemplateHazard.create(
      {
        template_id: tempId,
        hazard_id: hazardId,
        hazard_category_is_other: false,
        created_by: userId,
      },
      {transaction},
    );
  }
};

// Helper function to handle parameters
const handleParameters = async (
  template: ITemplateCreate,
  tempId: number,
  transaction: Transaction,
  userId?: string,
) => {
  for (const parameterData of template.parameters ?? []) {
    if (parameterData.is_other) {
      await TemplateParameter.create(
        {
          template_id: tempId,
          parameter_type_id: parameterData.parameter_type_id,
          parameter_is_other: true,
          created_by: userId,
          value: parameterData.value ?? '',
          status: BasicStatus.ACTIVE,
        },
        {transaction},
      );
    }
    for (const parameterId of parameterData.parameter_id) {
      await TemplateParameter.create(
        {
          template_id: tempId,
          parameter_type_id: parameterData.parameter_type_id,
          parameter_id: parameterId,
          parameter_is_other: false,
          created_by: userId,
          status: BasicStatus.ACTIVE,
        },
        {transaction},
      );
    }
  }
};

// Helper function to handle jobs
const handleJobs = async (
  template: ITemplateCreate,
  tempId: number,
  transaction: Transaction,
  userId?: string,
) => {
  for (const jobData of template.template_job ?? []) {
    const job = await TemplateJob.create(
      {
        template_id: tempId,
        job_step: jobData.job_step,
        job_hazard: jobData.job_hazard,
        job_nature_of_risk: jobData.job_nature_of_risk,
        job_existing_control: jobData.job_existing_control,
        job_additional_mitigation: jobData.job_additional_mitigation,
        job_close_out_date: jobData.job_close_out_date,
        job_close_out_responsibility_id:
          jobData.job_close_out_responsibility_id,
        created_by: userId,
        status: BasicStatus.ACTIVE,
      },
      {transaction},
    );

    for (const rating of jobData.template_job_initial_risk_rating ?? []) {
      await TemplateJobInitialRiskRating.create(
        {
          template_job_id: job.id,
          parameter_type_id: rating.parameter_type_id,
          rating: rating.rating,
          created_by: userId,
          status: BasicStatus.ACTIVE,
        },
        {transaction},
      );
    }

    for (const rating of jobData.template_job_residual_risk_rating ?? []) {
      await TemplateJobResidualRiskRating.create(
        {
          template_job_id: job.id,
          parameter_type_id: rating.parameter_type_id,
          rating: rating.rating,
          reason: rating.reason ?? '',
          created_by: userId,
          status: BasicStatus.ACTIVE,
        },
        {transaction},
      );
    }
  }
};

// Helper function to handle assessments
const handleAssessments = async (
  template: ITemplateCreate,
  tempId: number,
  transaction: Transaction,
  userId?: string,
) => {
  for (const assessment of template.template_task_reliability_assessment ??
    []) {
    await TemplateTaskReliabilityAssessment.create(
      {
        template_id: tempId,
        task_reliability_assessment_id:
          assessment.task_reliability_assessment_id,
        task_reliability_assessment_answer:
          assessment.task_reliability_assessment_answer,
        condition: assessment.condition,
        created_by: userId,
        status: BasicStatus.ACTIVE,
      },
      {transaction},
    );
  }
};

/**
 * @swagger
 * /templates/top:
 *   get:
 *     tags:
 *       - Template
 *     summary: Get top templates by risk count
 *     description: Retrieves the top templates based on the count of associated risks.
 *     parameters:
 *       - in: query
 *         name: maxCount
 *         schema:
 *           type: integer
 *         required: true
 *         description: Maximum number of results to return
 *     responses:
 *       200:
 *         description: List of top templates
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   template_count:
 *                     type: integer
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
export const getTopTemplatesHandler = createLambda(
  async ({queryStringParameters}: LambdaData): Promise<LambdaResponse> => {
    try {
      const maxCount = parseInt(queryStringParameters?.maxCount ?? '4');
      const topTemplates = (await Risk.findAll({
        attributes: [
          'template_id',
          [sq.fn('COUNT', sq.col('template_id')), 'template_count'],
        ],
        group: ['template_id'],
        order: [[sq.literal('template_count'), 'DESC']],
        limit: maxCount,
        raw: true,
      })) as unknown as TopTemplate[];
      const templateIds = topTemplates
        .map(t => t.template_id)
        .filter((id): id is number => id != null);

      // Map template counts for sorting
      const templateCountMap = new Map(
        topTemplates.map(t => [t.template_id, Number(t.template_count)]),
      );

      const results = await Template.findAll({
        where: {
          id: {[Op.in]: templateIds},
          status: RiskTemplateStatus.PUBLISHED,
        },
        attributes: ['id', 'task_requiring_ra', 'created_by', 'created_at'],
        include: [
          {
            model: TemplateCategory,
            as: 'template_category',
            required: false,
            where: {status: BasicStatus.ACTIVE},
            attributes: ['id', 'category_id'],
          },
          {
            model: TemplateHazard,
            as: 'template_hazards',
            required: false,
            where: {status: BasicStatus.ACTIVE},
            attributes: ['id', 'hazard_id'],
          },
          {
            model: TemplateKeyword,
            as: 'template_keywords',
            required: false,
            where: {status: BasicStatus.ACTIVE},
            attributes: ['id', 'name'],
          },
        ],
        raw: false,
      });
      const userIds = Array.from(
        new Set(results.map(row => row.created_by)),
      ).filter(Boolean) as string[];

      const userDetails: UserDetails[] = await fetchUserDetails(userIds);
      const sortedResults = results.sort((a, b) => {
        const countA = templateCountMap.get(a.id) || 0;
        const countB = templateCountMap.get(b.id) || 0;
        return countB - countA;
      });

      return {
        statusCode: StatusCodes.OK,
        response: {
          message: 'Top templates list',
          result: {
            results: sortedResults,
            userDetails,
          },
        },
      };
    } catch (error) {
      console.log('Error in getTopTemplatesHandler:', error);
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

export async function fetchUserDetails(
  userIds: string[],
): Promise<UserDetails[]> {
  if (!userIds || userIds.length === 0) return [];
  try {
    const response = (await Paris2Controller.getUserDetails(
      userIds,
    )) as UserResponse;
    return response.users.map(user => ({
      userId: user.id,
      email: user.email,
      full_name: user.full_name,
    }));
  } catch (error) {
    throw new CustomHttpError(500, 'Failed to fetch user details');
  }
}
