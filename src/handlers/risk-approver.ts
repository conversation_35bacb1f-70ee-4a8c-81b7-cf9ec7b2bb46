import {createLambda} from '../utils/lambda';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {RISK_DB} from '../db/db-client';
import {
  validateRiskApprover,
  RiskApproverInput,
  validateUpdateRiskApproverStatus,
  UpdateRiskApproverStatusInput,
  validateUpdateRiskApprovers,
  UpdateRiskApproversInput,
} from '../dtos/risk-approver.dto';
import Risk from '../models/risk.model';
import RiskApprover from '../models/risk-approver.model';
import sequelize from '../db/sequelize';
import {Op} from 'sequelize';
import {RiskApproverStatus} from '../enums/risk-approver-status.enum';
import {RiskApprovalStatus} from '../enums/risk-approval-status.enum';
import {RiskAssessmentRALevel} from '../enums/ra-level.enum';
import {handleNotFoundError} from '../error';
import {RiskAssessmentStatus} from '../enums';

/**
 * @swagger
 * /risk-approver:
 *   post:
 *     tags:
 *       - Risk Approver
 *     summary: Add approvers to a risk assessment
 *     description: Adds one or more approvers to a risk assessment based on the RA level
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - risk_id
 *               - ra_level
 *               - keycloak_id
 *             properties:
 *               risk_id:
 *                 type: number
 *                 description: ID of the risk assessment
 *               ra_level:
 *                 type: number
 *                 enum: [1, 2, 3]
 *                 description: Risk Assessment level (1=Low, 2=Medium, 3=High)
 *               keycloak_id:
 *                 type: array
 *                 items:
 *                   type: number
 *                 description: Array of approver keycloak IDs (1 approver for level 1, 3 approvers for level 2 and 3)
 *     responses:
 *       200:
 *         description: Risk approvers added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Risk approvers added successfully
 *                 risk_id:
 *                   type: number
 *                   example: 1
 *                 ra_level:
 *                   type: number
 *                   example: 2
 *       400:
 *         description: Invalid request body or validation error
 *       404:
 *         description: Risk assessment not found
 */
export const addRiskApproversHandler = createLambda(
  async ({jsonBody, user}: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sequelize.transaction();
    try {
      if (!jsonBody) {
        return {
          statusCode: 400,
          response: {message: 'Request body is required'},
        };
      }

      const input: RiskApproverInput = {
        risk_id: Number(jsonBody.risk_id),
        ra_level: jsonBody.ra_level,
        keycloak_id: jsonBody.keycloak_id,
      };

      const errors = validateRiskApprover(input);
      if (errors.length > 0) {
        await transaction.rollback();
        return {
          statusCode: 400,
          response: {message: errors.join(', ')},
        };
      }

      const risk = await Risk.findByPk(input.risk_id);
      if (!risk) {
        await transaction.rollback();
        return handleNotFoundError('Risk not found');
      }

      //---dont remove this code because it is used to check if the user is authorized to add approvers

      //  const existingApprover = await RiskApprover.findOne({
      //   where: {
      //     risk_id: input.risk_id,
      //     status: RiskApproverStatus.DEFAULT,
      //     keycloak_id: user?.user_id, // adjust as per your user identification logic
      //   },
      // });

      // if (!existingApprover) {
      //   await transaction.rollback();
      //   return {
      //     statusCode: 403,
      //     response: {
      //       message: 'You are not authorized to add risk approvers for this risk assessment',
      //     },
      //   };
      // }

      const approverPromises = input.keycloak_id.map(id =>
        RiskApprover.create(
          {
            risk_id: input.risk_id,
            keycloak_id: id,
            user_name: '',
            user_email: '',
            job_title: '',
            status: RiskApproverStatus.ACTIVE,
          },
          {transaction},
        ),
      );

      await Promise.all(approverPromises);

      await risk.update(
        {
          ra_level: input.ra_level,
        },
        {transaction},
      );

      await transaction.commit();

      return {
        statusCode: 200,
        response: {
          message: 'Risk approvers added successfully',
          risk_id: input.risk_id,
          ra_level: input.ra_level,
        },
      };
    } catch (error: any) {
      await transaction.rollback();
      return {
        statusCode: 400,
        response: {
          message:
            error instanceof Error
              ? error.message
              : 'Error adding risk approvers',
        },
      };
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

/**
 * @swagger
 * /risk-approver/{risk_id}/status:
 *   put:
 *     tags:
 *       - Risk Approver
 *     summary: Update risk approver status
 *     description: Update the approval status and optional message for a risk approver
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: risk_id
 *         required: true
 *         schema:
 *           type: number
 *         description: ID of the risk assessment
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: number
 *                 enum: [1, 2, 3]
 *                 description: Approval status (1=APPROVED, 2=REJECTED, 3=CONDITIONALLY_APPROVED)
 *               keycloak_id:
 *                 type: number
 *                 description: Keycloak ID of the user
 *               message:
 *                 type: string
 *                 description: Required for REJECTED and CONDITIONALLY_APPROVED status
 *     responses:
 *       200:
 *         description: Risk approver status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Risk approver status updated successfully
 *                 risk_id:
 *                   type: number
 *                   example: 1
 *                 approval_status:
 *                   type: number
 *                   example: 1
 *       400:
 *         description: Invalid request body, invalid status, or missing required message
 *       404:
 *         description: Risk approver not found
 */
export const updateRiskApproverStatusHandler = createLambda(
  async ({
    jsonBody,
    pathParameters,
    user,
  }: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sequelize.transaction();
    try {
      if (!jsonBody || !pathParameters?.risk_id) {
        return {
          statusCode: 400,
          response: {message: 'Request body and risk_id are required'},
        };
      }

      const input: UpdateRiskApproverStatusInput = {
        risk_id: Number(pathParameters.risk_id),
        status: Number(jsonBody.status),
        message: jsonBody.message,
      };

      // Validate input using Joi schema
      const errors = validateUpdateRiskApproverStatus(input);
      if (errors.length > 0) {
        await transaction.rollback();
        return {
          statusCode: 400,
          response: {message: errors.join(', ')},
        };
      }

      const riskApprover = await RiskApprover.findOne({
        where: {
          risk_id: input.risk_id,
          keycloak_id: jsonBody.keycloak_id,
          status: RiskApproverStatus.ACTIVE,
        },
        transaction,
      });

      if (!riskApprover) {
        await transaction.rollback();
        return handleNotFoundError('Risk approver not found');
      }

      // Create update object with correct types matching IRiskApprover interface
      const updateData = {
        approval_status: input.status as number,
        message: input.message as string | undefined,
        updated_by: user?.user_id as string | undefined,
      };

      await riskApprover.update(updateData, {transaction});

      if (input.status === RiskApprovalStatus.REJECTED) {
        await Risk.update(
          {status: RiskAssessmentStatus.REJECTED},
          {where: {id: input.risk_id}, transaction},
        );
      } else if (
        input.status === RiskApprovalStatus.APPROVED ||
        input.status === RiskApprovalStatus.CONDITIONALLY_APPROVED
      ) {
        const activeApprovers = await RiskApprover.findAll({
          where: {
            risk_id: input.risk_id,
            status: RiskApproverStatus.ACTIVE,
          },
        });

        const allApproved = activeApprovers.every(
          approver =>
            approver.approval_status === RiskApprovalStatus.APPROVED ||
            approver.approval_status ===
              RiskApprovalStatus.CONDITIONALLY_APPROVED,
        );

        if (allApproved && activeApprovers.length > 0) {
          await Risk.update(
            {status: RiskAssessmentStatus.APPROVED},
            {where: {id: input.risk_id}, transaction},
          );
        }
      }
      await transaction.commit();

      return {
        statusCode: 200,
        response: {
          message: 'Risk approver status updated successfully',
          risk_id: input.risk_id,
          approval_status: input.status,
        },
      };
    } catch (error: any) {
      await transaction.rollback();
      return {
        statusCode: 400,
        response: {
          message:
            error instanceof Error
              ? error.message
              : 'Error updating risk approver status',
        },
      };
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

/**
 * @swagger
 * /risk-approver/{risk_id}:
 *   put:
 *     tags:
 *       - Risk Approver
 *     summary: Update risk approvers and RA level
 *     description: Update the list of risk approvers and RA level for a risk assessment
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: risk_id
 *         required: true
 *         schema:
 *           type: number
 *         description: ID of the risk assessment
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ra_level
 *               - approvers
 *             properties:
 *               ra_level:
 *                 type: string
 *                 enum: ['ROUTINE', 'SPECIAL', 'CRITICAL']
 *                 description: Risk Assessment level
 *               approvers:
 *                 type: array
 *                 items:
 *                   type: number
 *                 description: Array of approver keycloak IDs
 *     responses:
 *       200:
 *         description: Risk approvers updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Risk approvers and RA level updated successfully
 *                 risk_id:
 *                   type: number
 *                   example: 1
 *                 ra_level:
 *                   type: string
 *                   example: SPECIAL
 *       400:
 *         description: Invalid request body or validation error
 *       404:
 *         description: Risk assessment not found
 */
export const updateRiskApproversHandler = createLambda(
  async ({
    jsonBody,
    pathParameters,
    user,
  }: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sequelize.transaction();
    try {
      if (!jsonBody || !pathParameters?.risk_id) {
        return {
          statusCode: 400,
          response: {message: 'Request body and risk_id are required'},
        };
      }

      const risk_id = Number(pathParameters.risk_id);
      const input: UpdateRiskApproversInput = {
        ra_level: jsonBody.ra_level,
        approvers: jsonBody.approvers,
      };

      // Validate input using Joi schema
      const errors = validateUpdateRiskApprovers(input);
      if (errors.length > 0) {
        await transaction.rollback();
        return {
          statusCode: 400,
          response: {message: errors.join(', ')},
        };
      }

      // Get current active approvers count
      const currentActiveApprovers = await RiskApprover.count({
        where: {
          risk_id,
          status: RiskApproverStatus.ACTIVE,
        },
      });
      // Get risk and current approvers
      const risk = await Risk.findByPk(risk_id);
      if (!risk) {
        await transaction.rollback();
        return handleNotFoundError('Risk not found');
      }
      // For SPECIAL and CRITICAL, ensure total active approvers will be 3 or more
      if (input.ra_level !== RiskAssessmentRALevel.ROUTINE) {
        const totalActiveApprovers =
          currentActiveApprovers + input.approvers.length;
        if (totalActiveApprovers < 3) {
          return {
            statusCode: 400,
            response: {
              message:
                'SPECIAL and CRITICAL levels require at least 3 active approvers in total',
            },
          };
        }
      }

      if (input.ra_level === RiskAssessmentRALevel.ROUTINE) {
        // Get all current active approvers
        const currentApprovers = await RiskApprover.findAll({
          where: {
            risk_id,
            status: RiskApproverStatus.ACTIVE,
            approval_status: RiskApprovalStatus.APPROVED,
            updated_by: {[Op.ne]: ''}, // Only get approvers that have been updated
          },
          order: [['updated_at', 'ASC']], // Order by update date to find latest
        });

        // Get the latest updated approver
        const latestUpdatedApprover = currentApprovers[0];

        if (latestUpdatedApprover) {
          // Keep latest updated approver active, mark others as inactive
          await Promise.all(
            currentApprovers
              .filter(approver => approver.id !== latestUpdatedApprover.id)
              .map(approver =>
                approver.update(
                  {status: RiskApproverStatus.INACTIVE},
                  {transaction},
                ),
              ),
          );

          await Risk.update(
            {status: RiskAssessmentStatus.APPROVED},
            {where: {id: risk_id}, transaction},
          );
        } else {
          // If no updated & approved approver exists, keep current user's approver active
          const allCurrentApprovers = await RiskApprover.findAll({
            where: {
              risk_id,
              status: RiskApproverStatus.ACTIVE,
            },
          });

          const currentUserApprover = allCurrentApprovers.find(
            approver => approver.keycloak_id === Number(user?.user_id),
          );

          if (currentUserApprover) {
            await Promise.all(
              allCurrentApprovers
                .filter(approver => approver.id !== currentUserApprover.id)
                .map(approver =>
                  approver.update(
                    {status: RiskApproverStatus.INACTIVE},
                    {transaction},
                  ),
                ),
            );
          }
        }
      } else {
        // Get current active approvers
        const currentApprovers = await RiskApprover.findAll({
          where: {
            risk_id,
            status: RiskApproverStatus.ACTIVE,
          },
        });

        // Calculate how many more approvers we need
        const neededApprovers = Math.max(0, 3 - currentApprovers.length);

        if (neededApprovers > 0) {
          // Only add new approvers up to the needed amount
          const newApproversToAdd = input.approvers.slice(0, neededApprovers);

          // Create new approvers
          const approverPromises = newApproversToAdd.map(id =>
            RiskApprover.create(
              {
                risk_id,
                keycloak_id: id,
                user_name: '',
                user_email: '',
                job_title: '',
                status: RiskApproverStatus.ACTIVE,
              },
              {transaction},
            ),
          );

          await Promise.all(approverPromises);
        }
      }

      // Update risk RA level
      await risk.update(
        {
          ra_level: input.ra_level,
        },
        {transaction},
      );

      await transaction.commit();

      return {
        statusCode: 200,
        response: {
          message: 'Risk approvers and RA level updated successfully',
          risk_id,
          ra_level: input.ra_level,
        },
      };
    } catch (error: any) {
      await transaction.rollback();
      return {
        statusCode: 400,
        response: {
          message:
            error instanceof Error
              ? error.message
              : 'Error updating risk approvers',
        },
      };
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);
