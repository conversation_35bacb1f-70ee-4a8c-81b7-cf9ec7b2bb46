import {handleNotFoundError} from '../error';
import {createLambda} from '../utils/lambda';
import Paris2Controller from '../controller/paris2-api-controller';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {RISK_DB} from '../db/db-client';
import {UserPermisssion} from '../enums/permissions.enum';

/**
 * @swagger
 * /vessel-ownership:
 *   get:
 *     tags:
 *       - Paris2
 *     summary: Retrieve a list of vessel ownerships
 *     description: Returns a filtered list of vessel ownerships. Optional `keyword` parameter is used to filter results.
 *     parameters:
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: Text to search vessel ownerships by keyword
 *     responses:
 *       200:
 *         description: A list of matching vessel ownerships
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 vesselOwnerships:
 *                   type: array
 *       401:
 *         description: Unauthorized (role missing or invalid token)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Unauthorized
 *       404:
 *         description: Path not found (for unsupported methods)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Path not found
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Internal error occurred
 */

export const getVesselOwnerships = createLambda(
  async ({
    method,
    queryStringParameters,
  }: LambdaData): Promise<LambdaResponse> => {
    if (method !== 'GET') {
      return handleNotFoundError('Path not found');
    }

    const keyword = queryStringParameters?.keyword ?? '';
    const vesselOwnerships =
      await Paris2Controller.getVesselOwnerships(keyword);

    return {
      statusCode: 200,
      response: vesselOwnerships.results,
    };
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: [UserPermisssion.MASTER_DATA_VIEW],
    validateAccess: async () => Promise.resolve(),
  },
);

/**
 * @swagger
 * /crew-list:
 *   get:
 *     tags:
 *       - Paris2
 *     summary: Retrieve crew list for a vessel
 *     description: Returns the crew list for a specific vessel and month. If month is not provided, returns data for current month.
 *     parameters:
 *       - in: query
 *         name: vessel_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the vessel to get crew list for
 *       - in: query
 *         name: month
 *         schema:
 *           type: string
 *           pattern: '^\d{4}-\d{2}$'
 *         description: Month in YYYY-MM format (optional, defaults to current month)
 *     responses:
 *       200:
 *         description: Successfully retrieved crew list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 crewList:
 *                   type: array
 *       400:
 *         description: Bad request (missing or invalid vessel_id)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: vessel_id is required
 *       401:
 *         description: Unauthorized (role missing or invalid token)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Unauthorized
 *       404:
 *         description: Path not found (for unsupported methods)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Path not found
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Internal error occurred
 */

export const getCrewList = createLambda(
  async ({
    method,
    queryStringParameters,
  }: LambdaData): Promise<LambdaResponse> => {
    if (method !== 'GET') {
      return handleNotFoundError('Path not found');
    }

    const vesselId = queryStringParameters?.vessel_id;
    if (!vesselId) {
      return {
        statusCode: 400,
        response: 'vessel_id is required',
      };
    }

    const month = queryStringParameters?.month;
    const crewList = await Paris2Controller.getCrewList(
      Number(vesselId),
      month,
    );

    return {
      statusCode: 200,
      response: {crewList},
    };
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: [UserPermisssion.MASTER_DATA_VIEW],
    validateAccess: async () => Promise.resolve(),
  },
);

/**
 * @swagger
 * /reporting-office:
 *   get:
 *     tags:
 *       - Paris2
 *     summary: Retrieve a list of reporting offices
 *     description: Returns all reporting offices from the paris2 API
 *     responses:
 *       200:
 *         description: Successfully retrieved reporting offices
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 reportingOffices:
 *                   type: array
 *       401:
 *         description: Unauthorized (role missing or invalid token)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Unauthorized
 *       404:
 *         description: Path not found (for unsupported methods)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Path not found
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Internal error occurred
 */

export const getReportingOffices = createLambda(
  async ({method}: LambdaData): Promise<LambdaResponse> => {
    if (method !== 'GET') {
      return handleNotFoundError('Path not found');
    }

    const reportingOffices = await Paris2Controller.getReportingOffices();

    return {
      statusCode: 200,
      response: reportingOffices,
    };
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: [UserPermisssion.MASTER_DATA_VIEW],
    validateAccess: async () => Promise.resolve(),
  },
);
