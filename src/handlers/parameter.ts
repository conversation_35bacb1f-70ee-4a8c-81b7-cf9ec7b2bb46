import {create<PERSON><PERSON><PERSON><PERSON>} from '../utils/lambda';
import Para<PERSON><PERSON>ontroller from '../controller/parameter.controller';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {RISK_DB} from '../db/db-client';
import {handleNotFoundError} from '../error';
import {UserPermisssion} from '../enums/permissions.enum';

/**
 * @swagger
 * /parameters:
 *   get:
 *     tags:
 *       - Parameters
 *     summary: Retrieve a list of parameters
 *     description: Returns a filtered list of parameters. Optional `search` parameter is used to filter results.

 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Text to search parameters by name or description
 *         example: parameter1
 *     responses:
 *       200:
 *         description: A list of matching parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 parameters:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "Parameter 1"
 *                       description:
 *                         type: string
 *                         example: "Description of Parameter 1"
 *       401:
 *         description: Unauthorized (role missing or invalid token)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Unauthorized
 *       404:
 *         description: Path not found (for unsupported methods)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Path not found
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Internal error occurred
 */
export const main = createLambda(
  async (data: LambdaData): Promise<LambdaResponse> => {
    if (data.method !== 'GET') {
      return handleNotFoundError('Path not found');
    }
    try {
      const search = data.queryStringParameters?.search ?? '';

      return {
        statusCode: 200,
        response: await ParameterController.list(search),
      };
    } catch (error: any) {
      console.error('Error handling parameters request:', error);
      throw error;
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: [UserPermisssion.MASTER_DATA_VIEW],
    validateAccess: async () => {
      return Promise.resolve();
    },
  },
);
