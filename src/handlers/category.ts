import {create<PERSON><PERSON>b<PERSON>} from '../utils/lambda';
import CategoryController from '../controller/category.controller';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {RISK_DB} from '../db/db-client';
import {handleNotFoundError} from '../error';
import {UserPermisssion} from '../enums/permissions.enum';

/**
 * @swagger
 * /categories:
 *   get:
 *     tags:
 *       - Category
 *     summary: Retrieve a list of categories
 *     description: Returns a filtered list of categories. Optional `search` parameter is used to filter results.

 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Text to search categories by name or description
 *         example: vessel
 *     responses:
 *       200:
 *         description: A list of matching categories
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 categories:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "Vessel Type"
 *                       description:
 *                         type: string
 *                         example: "List of vessel types used in risk assessment"
 *       401:
 *         description: Unauthorized (role missing or invalid token)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Unauthorized
 *       404:
 *         description: Path not found (for unsupported methods)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Path not found
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Internal error occurred
 */

export const main = createLambda(
  async (data: LambdaData): Promise<LambdaResponse> => {
    if (data.method !== 'GET') {
      return handleNotFoundError('Path not found');
    }
    try {
      const search = data.queryStringParameters?.search ?? '';

      return {
        statusCode: 200,
        response: await CategoryController.list(search),
      };
    } catch (error) {
      console.error('Error handling categories request:', error);
      throw error;
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: [UserPermisssion.MASTER_DATA_VIEW],
    validateAccess: async () => {
      return Promise.resolve();
    },
  },
);
