import {StatusC<PERSON>, BasicStatus} from '../enums';
import {
  handleError,
  handleValidationError,
  handleNotFoundError,
} from '../error';
import {sq, RISK_DB} from '../db/db-client';
import {Risk, RiskTeamMember} from '../models';
import {createLambda} from '../utils/lambda';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {RiskTeamMemberCreationAttributes} from '../models/risk-team-member.model';
import {updateTeamMembersSchema} from '../dtos/risk-team-member.dto';
import {UserPermisssion} from '../enums/permissions.enum';

type TeamMemberInput = Omit<
  RiskTeamMemberCreationAttributes,
  | 'risk_id'
  | 'status'
  | 'created_by'
  | 'updated_by'
  | 'created_at'
  | 'updated_at'
>;

interface UpdateTeamMembersRequest {
  team_members: TeamMemberInput[];
}

/**
 * @swagger
 * /risks/{risk_id}/team-members:
 *   put:
 *     tags:
 *       - Risk
 *     summary: Update team members for a risk
 *     description: Updates the list of team members associated with a risk
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: risk_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the risk
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               team_members:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     seafarer_id:
 *                       type: integer
 *                     seafarer_person_id:
 *                       type: integer
 *                     seafarer_hkid:
 *                       type: integer
 *                     seafarer_name:
 *                       type: string
 *                     seafarer_rank:
 *                       type: string
 *                     seafarer_rank_id:
 *                       type: integer
 *                     seafarer_rank_sort_order:
 *                       type: string
 *     responses:
 *       200:
 *         description: Team members updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Risk not found
 *       500:
 *         description: Internal server error
 */
export const updateRiskTeamMembersHandler = createLambda(
  async ({
    jsonBody,
    pathParameters,
    user,
  }: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sq.transaction();
    try {
      const riskId = pathParameters?.risk_id;

      if (!riskId) {
        return handleValidationError('Risk ID is required');
      }

      // Validate risk exists
      const risk = await Risk.findByPk(riskId, {transaction});
      if (!risk) {
        await transaction.rollback();
        return handleNotFoundError('Risk not found');
      }

      // Validate request body
      const {error, value} = updateTeamMembersSchema.validate(jsonBody);
      if (error) {
        return handleValidationError(error.message);
      }

      const requestData = value as UpdateTeamMembersRequest;

      // Get existing active team members
      const existingMembers = await RiskTeamMember.findAll({
        where: {
          risk_id: riskId,
          status: BasicStatus.ACTIVE,
        },
      });

      // Process team members
      const incomingMembers = requestData.team_members;
      const incomingSeafarerIds = incomingMembers.map(m => m.seafarer_id!);

      // Mark members not in incoming list as inactive
      await Promise.all(
        existingMembers
          .filter(
            member =>
              member.seafarer_id &&
              !incomingSeafarerIds.includes(member.seafarer_id),
          )
          .map(member =>
            member.update(
              {
                status: BasicStatus.INACTIVE,
                updated_by: user?.user_id,
              },
              {transaction},
            ),
          ),
      );

      // Process incoming members
      for (const member of incomingMembers) {
        const existingMember = existingMembers.find(
          m => m.seafarer_id === member.seafarer_id,
        );

        const memberData: RiskTeamMemberCreationAttributes = {
          risk_id: Number(riskId),
          seafarer_id: member.seafarer_id,
          seafarer_person_id: member.seafarer_person_id || undefined,
          seafarer_hkid: member.seafarer_hkid || undefined,
          seafarer_name: member.seafarer_name,
          seafarer_rank: member.seafarer_rank,
          seafarer_rank_id: member.seafarer_rank_id || undefined,
          seafarer_rank_sort_order:
            member.seafarer_rank_sort_order || undefined,
          status: BasicStatus.ACTIVE,
          created_by: user?.user_id,
        };

        if (!existingMember) {
          // New member - create
          await RiskTeamMember.create(memberData, {transaction});
        } else if (existingMember.status === BasicStatus.INACTIVE) {
          // Existing inactive member - create new active record
          await RiskTeamMember.create(memberData, {transaction});
        }
        // If member exists and is active, do nothing
      }

      await transaction.commit();

      return {
        statusCode: StatusCodes.OK,
        response: {
          message: 'Team members updated successfully',
        },
      };
    } catch (error) {
      await transaction.rollback();
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: [UserPermisssion.RA_CREATE],
    validateAccess: async () => Promise.resolve(),
  },
);
