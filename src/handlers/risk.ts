import {Transaction, Op, Model} from 'sequelize';
import {
  StatusCodes,
  RiskAssessmentStatus,
  BasicStatus,
  RiskAssessmentRALevel,
} from '../enums';
import {
  handleError,
  handleValidationError,
  handleNotFoundError,
} from '../error';
import {
  validateRisk,
  validateRiskVesselDetails,
  validateRiskTeamMembers,
} from '../validation/risk.validation';
import {sq, RISK_DB} from '../db/db-client';
import {
  Category,
  Hazard,
  Parameter,
  ParameterType,
  Risk,
  RiskTeamMember,
  RiskCategory,
  RiskHazard,
  RiskParameter,
  RiskJob,
  RiskJobInitialRiskRating,
  RiskJobResidualRiskRating,
  RiskTaskReliabilityAssessment,
  RiskApprover,
} from '../models';
import {IRiskCreate, IRiskAttributes} from '../types/risk.types';
import {createLambda} from '../utils/lambda';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {getRiskListSchema, RiskListQueryParams} from '../dtos/risk.dto';
import {RiskApproverStatus} from '../enums/risk-approver-status.enum';
import RiskApprovalRequired from '../models/risk-approval-required.model';
import ApprovalRequired from '../models/approval-required.model';

// Type helpers
type CreateAttributes<T> = Omit<T, 'id'>;

const createRisk = async (
  risk: IRiskCreate,
  transaction: Transaction,
  userId?: string,
  riskId?: number,
): Promise<IRiskAttributes | IRiskCreate> => {
  let tempId = riskId;
  let riskRecord;

  if (!riskId) {
    riskRecord = await Risk.create(
      {
        template_id: risk.template_id,
        task_requiring_ra: risk.task_requiring_ra,
        assessor: risk.assessor,
        vessel_ownership_id: risk.vessel_ownership_id,
        vessel_id: risk.vessel_id,
        vessel_code: risk.vessel_code,
        vessel_name: risk.vessel_name,
        vessel_category: risk.vessel_category,
        vessel_tech_group: risk.vessel_tech_group,
        office_id: risk.office_id,
        office_name: risk.office_name,
        date_risk_assessment: risk.date_risk_assessment,
        task_duration: risk.task_duration,
        ra_level: risk.ra_level,
        task_alternative_consideration: risk.task_alternative_consideration,
        task_rejection_reason: risk.task_rejection_reason,
        worst_case_scenario: risk.worst_case_scenario,
        recovery_measures: risk.recovery_measures,
        status:
          risk.status === 'PUBLISHED'
            ? RiskAssessmentStatus.PUBLISHED
            : RiskAssessmentStatus.DRAFT,
        created_by: userId,
        publish_on: risk.status === 'PUBLISHED' ? new Date() : null,
        draft_step: risk.draft_step,
      } as CreateAttributes<IRiskAttributes>,
      {transaction},
    );
    tempId = riskRecord.id;
  }

  // Add team members
  if (
    Array.isArray(risk.risk_team_member) &&
    risk.risk_team_member.length > 0
  ) {
    for (const member of risk.risk_team_member) {
      await RiskTeamMember.create(
        {
          risk_id: tempId!,
          seafarer_id: member.seafarer_id,
          seafarer_person_id: member.seafarer_person_id,
          seafarer_hkid: member.seafarer_hkid,
          seafarer_name: member.seafarer_name,
          seafarer_rank: member.seafarer_rank,
          seafarer_rank_id: member.seafarer_rank_id,
          seafarer_rank_sort_order: member.seafarer_rank_sort_order,
          status: BasicStatus.ACTIVE,
          created_by: userId,
        },
        {transaction},
      );
    }
  }

  // Add categories
  if (risk.risk_category?.category_id) {
    for (const categoryId of risk.risk_category.category_id) {
      await RiskCategory.create(
        {
          risk_id: tempId!,
          category_id: categoryId,
          category_is_other: false,
          status: BasicStatus.ACTIVE,
          created_by: userId,
        },
        {transaction},
      );
    }
  }

  //---Add approval required
  if (risk.approval_required) {
    for (const approvalId of risk.approval_required) {
      await RiskApprovalRequired.create(
        {
          risk_id: tempId!,
          approval_required_id: approvalId,
          created_by: userId,
        },
        {transaction},
      );
    }
  }

  // Add hazards
  if (risk.risk_hazard) {
    let otherId = -1;
    if (risk.risk_hazard.is_other && risk.risk_hazard.value) {
      await RiskHazard.create(
        {
          risk_id: tempId!,
          hazard_category_is_other: true,
          value: risk.risk_hazard.value,
          status: BasicStatus.ACTIVE,
          created_by: userId,
        },
        {transaction},
      );
    }
    if (Array.isArray(risk.risk_hazard.hazard_id)) {
      for (const hazardId of risk.risk_hazard.hazard_id) {
        await RiskHazard.create(
          {
            risk_id: tempId!,
            hazard_id: hazardId,
            hazard_category_is_other: otherId === hazardId,
            status: BasicStatus.ACTIVE,
            created_by: userId,
          },
          {transaction},
        );
      }
    }
  }

  // Add parameters
  if (Array.isArray(risk.parameters)) {
    for (const parameterData of risk.parameters) {
      if (parameterData.is_other && parameterData.value) {
        await RiskParameter.create(
          {
            risk_id: tempId!,
            parameter_type_id: parameterData.parameter_type_id,
            parameter_is_other: true,
            value: parameterData.value,
            status: BasicStatus.ACTIVE,
            created_by: userId,
          },
          {transaction},
        );
      }
      if (Array.isArray(parameterData.parameter_id)) {
        for (const parameterId of parameterData.parameter_id) {
          await RiskParameter.create(
            {
              risk_id: tempId!,
              parameter_type_id: parameterData.parameter_type_id,
              parameter_id: parameterId,
              parameter_is_other: false,
              status: BasicStatus.ACTIVE,
              created_by: userId,
            },
            {transaction},
          );
        }
      }
    }
  }

  // Add jobs
  if (Array.isArray(risk.risk_job)) {
    for (const jobData of risk.risk_job) {
      const job = await RiskJob.create(
        {
          risk_id: tempId!,
          job_step: jobData.job_step,
          job_hazard: jobData.job_hazard,
          job_nature_of_risk: jobData.job_nature_of_risk,
          job_existing_control: jobData.job_existing_control,
          job_additional_mitigation: jobData.job_additional_mitigation,
          job_close_out_date: jobData.job_close_out_date,
          job_close_out_responsibility_id:
            jobData.job_close_out_responsibility_id,
          status: BasicStatus.ACTIVE,
          created_by: userId,
        },
        {transaction},
      );

      // Add initial risk ratings
      if (Array.isArray(jobData.risk_job_initial_risk_rating)) {
        for (const rating of jobData.risk_job_initial_risk_rating) {
          await RiskJobInitialRiskRating.create(
            {
              risk_job_id: job.id!,
              parameter_type_id: rating.parameter_type_id,
              rating: rating.rating,
              status: BasicStatus.ACTIVE,
              created_by: userId,
            },
            {transaction},
          );
        }
      }

      // Add residual risk ratings
      if (Array.isArray(jobData.risk_job_residual_risk_rating)) {
        for (const rating of jobData.risk_job_residual_risk_rating) {
          await RiskJobResidualRiskRating.create(
            {
              risk_job_id: job.id!,
              parameter_type_id: rating.parameter_type_id,
              rating: rating.rating,
              reason: rating.reason || '',
              status: BasicStatus.ACTIVE,
              created_by: userId,
            },
            {transaction},
          );
        }
      }
    }
  }

  // Add task reliability assessments
  if (Array.isArray(risk.risk_task_reliability_assessment)) {
    for (const assessment of risk.risk_task_reliability_assessment) {
      await RiskTaskReliabilityAssessment.create(
        {
          risk_id: tempId!,
          task_reliability_assessment_id:
            assessment.task_reliability_assessment_id,
          task_reliability_assessment_answer:
            assessment.task_reliability_assessment_answer,
          condition: assessment.condition,
          status: BasicStatus.ACTIVE,
          created_by: userId,
        },
        {transaction},
      );
    }
  }

  // Add approver data when risk is published
  if (risk.status === 'PUBLISHED') {
    if (risk.ra_level === RiskAssessmentRALevel.LEVEL1) {
      // For LEVEL1, use the same user and set as approved
      const userEmail = userId
        ? `${userId}@example.com`
        : '<EMAIL>';
      const encodedEmail = Buffer.from(userEmail).toString('base64');
      await RiskApprover.create<Model<any>>(
        {
          risk_id: tempId!,
          keycloak_id: risk.assessor,
          user_name: userId || 'System User',
          user_email: encodedEmail,
          job_title: 'Assessor',
          approval_status: RiskAssessmentStatus.APPROVED,
          status: RiskApproverStatus.DEFAULT,
          created_by: userId,
        },
        {transaction},
      );

      await Risk.update(
        {status: RiskAssessmentStatus.APPROVED},
        {
          where: {id: 1},
          transaction,
        },
      );
    } else {
      // For other levels, create with random data
      const randomNumber = Math.floor(Math.random() * 100);
      const rawEmail = `approver${randomNumber}@example.com`;
      const encodedEmail = Buffer.from(rawEmail).toString('base64');
      await RiskApprover.create<Model<any>>(
        {
          risk_id: tempId!,
          keycloak_id: Math.floor(Math.random() * 1000) + 1,
          user_name: `Approver ${Math.floor(Math.random() * 100)}`,
          user_email: encodedEmail,
          job_title: 'Safety Officer',
          approval_status: RiskAssessmentStatus.PUBLISHED,
          status: RiskApproverStatus.DEFAULT,
          created_by: userId,
        },
        {transaction},
      );
    }
  }

  return riskId ? risk : riskRecord!;
};

// POST /risks handler
/**
 * @swagger
 * /risks:
 *   post:
 *     tags:
 *       - Risk
 *     summary: Create a new risk assessment
 *     description: Creates a new risk assessment with the provided details.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               template_id:
 *                 type: integer
 *                 description: ID of the template to base the risk assessment on
 *               task_requiring_ra:
 *                 type: string
 *                 description: Task requiring risk assessment
 *               assessor:
 *                 type: integer
 *                 description: ID of the assessor
 *               vessel_ownership_id:
 *                 type: integer
 *                 description: ID of the vessel ownership
 *               date_risk_assessment:
 *                 type: string
 *                 format: date
 *                 description: Date of risk assessment
 *               task_duration:
 *                 type: integer
 *                 description: Duration of the task
 *               ra_level:
 *                 type: string
 *                 enum: [LEVEL1, LEVEL2, LEVEL3]
 *                 description: Risk assessment level
 *     responses:
 *       201:
 *         description: Risk assessment created successfully
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
export const createRiskHandler = createLambda(
  async ({jsonBody, user}: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sq.transaction();
    try {
      const risk = jsonBody as IRiskCreate;
      const isPublish = risk.status === 'PUBLISHED';
      let validationErrors = validateRisk(risk, isPublish);

      // Perform additional validations for vessel assessor
      if (risk.assessor === 2) {
        // Check vessel ownership ID
        if (!risk.vessel_ownership_id) {
          validationErrors.push(
            'Vessel ownership ID is required for vessel assessor',
          );
        } else {
          // Get and validate vessel details
          const [errors, vesselDetails] = await validateRiskVesselDetails(
            risk.vessel_ownership_id,
          );
          validationErrors = [...validationErrors, ...errors];

          // If vessel validation passed, update risk object with vessel details
          if (vesselDetails) {
            risk.vessel_id = vesselDetails.vessel_id;
            risk.vessel_code = vesselDetails.vessel_code;
            risk.vessel_name = vesselDetails.vessel_name;
            risk.vessel_tech_group = vesselDetails.vessel_tech_group;
            risk.vessel_category = vesselDetails.vessel_category;

            // Validate team members if present
            if (
              vesselDetails.vessel_id &&
              Array.isArray(risk.risk_team_member) &&
              risk.risk_team_member.length > 0
            ) {
              const teamErrors = await validateRiskTeamMembers(
                vesselDetails.vessel_id,
                risk.risk_team_member,
              );
              validationErrors = [...validationErrors, ...teamErrors];
            }
          }
        }
      }

      if (validationErrors.length > 0) {
        return handleValidationError(validationErrors.join(', '));
      }
      const riskRecord = await createRisk(risk, transaction, user?.user_id);
      await transaction.commit();
      return {
        statusCode: StatusCodes.CREATED,
        response: riskRecord,
      };
    } catch (error) {
      await transaction.rollback();
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// PATCH /risk/{id} handler
/**
 * @swagger
 * /risks/{id}:
 *   patch:
 *     tags:
 *       - Risk
 *     summary: Update an existing risk assessment
 *     description: Updates an existing risk assessment with the provided details.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the risk assessment to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Risk'
 *     responses:
 *       200:
 *         description: Risk assessment updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Risk assessment not found
 *       500:
 *         description: Internal server error
 */
export const patchRiskHandler = createLambda(
  async ({
    jsonBody,
    pathParameters,
    user,
  }: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sq.transaction();
    try {
      const risk = jsonBody as IRiskCreate;
      const riskId = pathParameters?.id;

      if (!riskId) {
        return handleValidationError('Risk ID is required');
      }

      const existingRisk = await Risk.findByPk(riskId);
      if (!existingRisk) {
        return handleValidationError('Risk not found');
      }

      const isPublish = risk.status === 'PUBLISHED';
      let validationErrors = validateRisk(risk, isPublish);

      // Perform additional validations for vessel assessor
      if (risk.assessor === 2) {
        if (!risk.vessel_ownership_id) {
          validationErrors.push(
            'Vessel ownership ID is required for vessel assessor',
          );
        } else {
          // Get and validate vessel details
          const [errors, vesselDetails] = await validateRiskVesselDetails(
            risk.vessel_ownership_id,
          );
          validationErrors = [...validationErrors, ...errors];

          // If vessel validation passed, update risk object with vessel details
          if (vesselDetails) {
            risk.vessel_id = vesselDetails.vessel_id;
            risk.vessel_code = vesselDetails.vessel_code;
            risk.vessel_name = vesselDetails.vessel_name;
            risk.vessel_tech_group = vesselDetails.vessel_tech_group;
            risk.vessel_category = vesselDetails.vessel_category;

            // Validate team members if present
            if (
              vesselDetails.vessel_id &&
              Array.isArray(risk.risk_team_member) &&
              risk.risk_team_member.length > 0
            ) {
              const teamErrors = await validateRiskTeamMembers(
                vesselDetails.vessel_id,
                risk.risk_team_member,
              );
              validationErrors = [...validationErrors, ...teamErrors];
            }
          }
        }
      }

      if (validationErrors.length > 0) {
        return handleValidationError(validationErrors.join(', '));
      }

      // Delete existing related records
      await Promise.all([
        RiskTeamMember.destroy({where: {risk_id: riskId}, transaction}),
        RiskApprovalRequired.destroy({where: {risk_id: riskId}, transaction}),
        RiskCategory.destroy({where: {risk_id: riskId}, transaction}),
        RiskHazard.destroy({where: {risk_id: riskId}, transaction}),
        RiskParameter.destroy({where: {risk_id: riskId}, transaction}),
        RiskTaskReliabilityAssessment.destroy({
          where: {risk_id: riskId},
          transaction,
        }),
      ]);

      // Delete job related records
      const jobs = await RiskJob.findAll({where: {risk_id: riskId}});
      for (const job of jobs) {
        await Promise.all([
          RiskJobInitialRiskRating.destroy({
            where: {risk_job_id: job.id},
            transaction,
          }),
          RiskJobResidualRiskRating.destroy({
            where: {risk_job_id: job.id},
            transaction,
          }),
        ]);
      }
      await RiskJob.destroy({where: {risk_id: riskId}, transaction});

      // Update risk
      await existingRisk.update(
        {
          task_requiring_ra: risk.task_requiring_ra,
          assessor: risk.assessor,
          vessel_ownership_id: risk.vessel_ownership_id,
          vessel_id: risk.vessel_id,
          vessel_code: risk.vessel_code,
          vessel_name: risk.vessel_name,
          vessel_category: risk.vessel_category,
          vessel_tech_group: risk.vessel_tech_group,
          office_id: risk.office_id,
          office_name: risk.office_name,
          date_risk_assessment: risk.date_risk_assessment,
          task_duration: risk.task_duration,
          ra_level: risk.ra_level,
          task_alternative_consideration: risk.task_alternative_consideration,
          task_rejection_reason: risk.task_rejection_reason,
          worst_case_scenario: risk.worst_case_scenario,
          recovery_measures: risk.recovery_measures,
          status:
            risk.status === 'PUBLISHED'
              ? RiskAssessmentStatus.PUBLISHED
              : RiskAssessmentStatus.DRAFT,
          updated_by: user?.user_id,
          publish_on: risk.status === 'PUBLISHED' ? new Date() : null,
          draft_step: risk.draft_step,
        } as Partial<IRiskAttributes>,
        {transaction},
      );

      // Create new records
      await createRisk(risk, transaction, user?.user_id, existingRisk.id);
      await transaction.commit();

      return {
        statusCode: StatusCodes.OK,
        response: {
          message: 'Risk updated successfully',
          result: existingRisk,
        },
      };
    } catch (error) {
      await transaction.rollback();
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// GET /risk/{id} handler
/**
 * @swagger
 * /risks/{id}:
 *   get:
 *     tags:
 *       - Risk
 *     summary: Get a risk assessment by ID
 *     description: Retrieves a risk assessment by its ID with all associated data.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the risk assessment to retrieve
 *     responses:
 *       200:
 *         description: Risk assessment retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Risk'
 *       404:
 *         description: Risk assessment not found
 *       500:
 *         description: Internal server error
 */
export const getRiskByIdHandler = createLambda(
  async ({pathParameters}: LambdaData): Promise<LambdaResponse> => {
    try {
      const riskId = pathParameters?.id;

      if (!riskId) {
        return handleValidationError('Risk ID is required');
      }

      const risk = await Risk.findByPk(riskId, {
        include: [
          {
            model: RiskTeamMember,
            as: 'risk_team_member',
          },
          {
            model: RiskCategory,
            as: 'risk_category',
            include: [{model: Category, as: 'category', attributes: ['name']}],
          },
          {
            model: RiskHazard,
            as: 'risk_hazards',
            include: [
              {
                model: Hazard,
                as: 'hazard_detail',
                attributes: ['name'],
              },
            ],
          },
          {
            model: RiskParameter,
            as: 'risk_parameter',
            include: [
              {
                model: ParameterType,
                as: 'parameterType',
                attributes: ['id', 'name'],
              },
              {
                model: Parameter,
                as: 'parameter',
                attributes: ['id', 'name'],
              },
            ],
          },
          {
            model: RiskJob,
            as: 'risk_job',
            include: [
              {
                model: RiskJobInitialRiskRating,
                as: 'risk_job_initial_risk_rating',
              },
              {
                model: RiskJobResidualRiskRating,
                as: 'risk_job_residual_risk_rating',
              },
            ],
          },
          {
            model: RiskTaskReliabilityAssessment,
            as: 'risk_task_reliability_assessment',
          },
          {model: RiskApprover, as: 'risk_approver'},
          {
            model: RiskApprovalRequired,
            as: 'risk_approval_required',
            attributes: ['id', 'approval_required_id'],
            include: [
              {
                model: ApprovalRequired,
                as: 'approval_required',
                attributes: ['name'],
              },
            ],
          },
        ],
      });

      if (!risk) {
        return handleNotFoundError('Risk not found');
      }

      return {
        statusCode: StatusCodes.OK,
        response: {message: 'Risk details', result: risk},
      };
    } catch (error) {
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// GET /risk handler
/**
 * @swagger
 * /risks:
 *   get:
 *     tags:
 *       - Risk
 *     summary: Get a list of risk assessments
 *     description: Retrieves a paginated list of risk assessments with optional filters.
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter risk assessments
 *       - in: query
 *         name: vessel_name
 *         schema:
 *           type: string
 *         description: Filter by vessel name
 *       - in: query
 *         name: office_name
 *         schema:
 *           type: string
 *         description: Filter by office name
 *       - in: query
 *         name: vessel_category
 *         schema:
 *           type: string
 *         description: Filter by vessel category
 *       - in: query
 *         name: ra_level
 *         schema:
 *           type: string
 *           enum: [LEVEL1, LEVEL2, LEVEL3]
 *         description: Filter by risk assessment level
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status (e.g., DRAFT, PUBLISHED)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of risk assessments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Risk'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *       500:
 *         description: Internal server error
 */
export const getRiskListHandler = createLambda(
  async ({
    queryStringParameters,
    multiValueQueryStringParameters,
  }: LambdaData): Promise<LambdaResponse> => {
    try {
      const formattedQuery = formatQueryParams(
        queryStringParameters,
        multiValueQueryStringParameters,
      );
      const {error, value} = getRiskListSchema.validate(formattedQuery);
      if (error) {
        return handleValidationError(error.message);
      }

      const {
        search,
        'vessel_id[]': vesselIds,
        'office_id[]': officeIds,
        'vessel_category[]': vesselCategories,
        ra_level,
        submitted_on,
        assessment_date,
        approval_date,
        'approval_status[]': approvalStatuses,
        status = RiskAssessmentStatus.PUBLISHED,
        page = 1,
        limit = 10,
        sort_by = 'created_at',
        sort_order = 'ASC',
      } = value as RiskListQueryParams;

      const offset = (page - 1) * limit;

      if (status === RiskAssessmentStatus.DRAFT) {
        const draftResult = await Risk.findAndCountAll({
          where: {status: RiskAssessmentStatus.DRAFT},
          limit,
          attributes: ['id', 'task_requiring_ra', 'updated_at', 'created_at'],
          offset,
          order: [[sort_by, sort_order]],
        });
        const totalItems = draftResult.count;
        const totalPages = Math.ceil(totalItems / limit);
        return {
          statusCode: StatusCodes.OK,
          response: {
            message: 'Risk Draft list',
            result: {
              data: draftResult.rows,
              pagination: {
                totalItems,
                totalPages,
                page,
                pageSize: limit,
              },
            },
          },
        };
      }

      const whereClause: any = {
        status,
      };

      if (search?.trim()) {
        whereClause.task_requiring_ra = {[Op.iLike]: `%${search.trim()}%`};
      }

      if (ra_level) {
        whereClause.ra_level = ra_level;
      }

      const addDateFilter = (
        dateRange: {start_date?: Date; end_date?: Date} | undefined,
        fieldName: string,
      ) => {
        if (!dateRange) return;

        whereClause[fieldName] = whereClause[fieldName] || {};

        if (dateRange.start_date) {
          const start = new Date(dateRange.start_date);
          start.setHours(0, 0, 0, 0);
          whereClause[fieldName][Op.gte] = start;
        }

        if (dateRange.end_date) {
          const end = new Date(dateRange.end_date);
          end.setHours(23, 59, 59, 999);
          whereClause[fieldName][Op.lte] = end;
        }
      };

      addDateFilter(submitted_on, 'publish_on');
      addDateFilter(assessment_date, 'date_risk_assessment');
      addDateFilter(approval_date, 'approval_date');

      if (approvalStatuses?.length) {
        whereClause.status = {[Op.in]: status};
      }

      if (vesselIds?.length) {
        whereClause.vessel_id = {[Op.in]: vesselIds};
      }

      if (vesselCategories?.length) {
        whereClause.vessel_category = {[Op.in]: vesselCategories};
      }

      if (officeIds?.length) {
        whereClause.office_id = {[Op.in]: officeIds};
      }

      const result = await Risk.findAndCountAll({
        where: whereClause,
        distinct: true,
        order: [[sort_by, sort_order]],
        limit,
        offset,
      });

      const totalItems = result.count;
      const totalPages = Math.ceil(totalItems / limit);

      return {
        statusCode: StatusCodes.OK,
        response: {
          message: 'Risk list',
          result: {
            data: result.rows,
            pagination: {
              totalItems,
              totalPages,
              page,
              pageSize: limit,
            },
          },
        },
      };
    } catch (error) {
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// Helper function to format query parameters
function formatQueryParams(
  queryStringParams: {[key: string]: string} = {},
  multiValueParams: {[key: string]: string[]} | null = null,
): Record<string, any> {
  const params: Record<string, any> = {...queryStringParams};

  // Handle array fields
  const arrayFields = [
    'approval_status[]',
    'vessel_id[]',
    'office_id[]',
    'vessel_category[]',
  ];
  arrayFields.forEach(field => {
    if (multiValueParams?.[field]) {
      params[field] = multiValueParams[field].map(val =>
        isNaN(+val) ? val : Number(val),
      );
    } else {
      params[field] = [];
    }
  });

  // Handle date range fields
  const dateRangeFields = ['submitted_on', 'assessment_date', 'approval_date'];
  dateRangeFields.forEach(field => {
    const startDate = params[`${field}_start`];
    const endDate = params[`${field}_end`];
    if (startDate || endDate) {
      params[field] = {
        start_date: startDate ? new Date(startDate) : undefined,
        end_date: endDate ? new Date(endDate) : undefined,
      };
    }
  });

  return params;
}

// DELETE /risk/{id} handler
/**
 * @swagger
 * /risks/{id}:
 *   delete:
 *     tags:
 *       - Risk
 *     summary: Delete a risk assessment
 *     description: Deletes a risk assessment by its ID. Only risk assessments with DRAFT status can be deleted.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the risk assessment to delete
 *     responses:
 *       200:
 *         description: Risk assessment deleted successfully
 *       400:
 *         description: Cannot delete non-draft risk assessment
 *       404:
 *         description: Risk assessment not found
 *       500:
 *         description: Internal server error
 */
export const deleteRiskHandler = createLambda(
  async ({pathParameters}: LambdaData): Promise<LambdaResponse> => {
    const transaction = await sq.transaction();
    try {
      const riskId = pathParameters?.id;

      if (!riskId) {
        return handleValidationError('Risk ID is required');
      }

      const existingRisk = await Risk.findByPk(riskId);
      if (!existingRisk) {
        return handleNotFoundError('Risk not found');
      }

      if (existingRisk.status !== RiskAssessmentStatus.DRAFT) {
        return handleValidationError(
          'Only risk with status DRAFT can be deleted',
        );
      }

      const [jobs] = await Promise.all([
        RiskJob.findAll({where: {risk_id: riskId}}),
        RiskTeamMember.destroy({where: {risk_id: riskId}, transaction}),
        RiskApprovalRequired.destroy({where: {risk_id: riskId}, transaction}),
        RiskCategory.destroy({where: {risk_id: riskId}, transaction}),
        RiskHazard.destroy({where: {risk_id: riskId}, transaction}),
        RiskParameter.destroy({where: {risk_id: riskId}, transaction}),
        RiskTaskReliabilityAssessment.destroy({
          where: {risk_id: riskId},
          transaction,
        }),
        RiskApprover.destroy({where: {risk_id: riskId}, transaction}),
      ]);

      const jobIds = jobs.map(job => Number(job.id));
      await Promise.all([
        RiskJobInitialRiskRating.destroy({
          where: {risk_job_id: jobIds},
          transaction,
        }),
        RiskJobResidualRiskRating.destroy({
          where: {risk_job_id: jobIds},
          transaction,
        }),
      ]);

      await RiskJob.destroy({where: {risk_id: riskId}, transaction});
      await Risk.destroy({where: {id: riskId}, transaction});
      await transaction.commit();

      return {
        statusCode: StatusCodes.OK,
        response: {message: 'Risk deleted successfully', result: true},
      };
    } catch (error) {
      await transaction.rollback();
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);

// GET /risk/options/{key} handler
/**
 * @swagger
 * /risks/options/{key}:
 *   get:
 *     tags:
 *       - Risk
 *     summary: Get distinct options for a risk field
 *     description: Returns a list of distinct values for a specified risk field (vessel_name, vessel_category, vessel_tech_group, or office_name).
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *           enum: [vessel_category, vessel_tech_group]
 *         description: The field to get options for
 *     responses:
 *       200:
 *         description: List of distinct options for the given key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 result:
 *                   type: array
 *                   items:
 *                     type: object
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
export const getRiskOptionsByKeyHandler = createLambda(
  async ({pathParameters}: LambdaData): Promise<LambdaResponse> => {
    try {
      type ValidKey = 'vessel_tech_group' | 'vessel_category';
      const key = pathParameters?.key as ValidKey | undefined;
      const validKeys: ValidKey[] = ['vessel_tech_group', 'vessel_category'];

      if (!key) {
        return handleValidationError('Key parameter is required');
      }

      if (!validKeys.includes(key)) {
        return handleValidationError(
          `Invalid key: ${key}. Valid keys are: ${validKeys.join(', ')}`,
        );
      }

      const result = await Risk.findAll({
        attributes: [[sq.fn('DISTINCT', sq.col(key)), key]],
        where: {
          [key]: {
            [Op.and]: [{[Op.ne]: null}, {[Op.ne]: ''}],
          },
          status: RiskAssessmentStatus.PUBLISHED,
        },
        order: [[sq.col(key), 'ASC']],
        raw: true,
      });

      return {
        statusCode: StatusCodes.OK,
        response: {
          message: `Risk ${key} options`,
          result: result.map(item => item[key]),
        },
      };
    } catch (error) {
      return handleError(error);
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => Promise.resolve(),
  },
);
