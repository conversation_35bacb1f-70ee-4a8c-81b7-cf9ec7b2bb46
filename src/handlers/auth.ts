import {JwtPayload, verify} from 'jsonwebtoken';
import {omitBy, isUndefined} from 'lodash';
import {
  APIGatewayAuthorizerResult,
  APIGatewayAuthorizerResultContext,
  Context,
  Callback,
} from 'aws-lambda';
import {UserPermisssion} from '../enums/permissions.enum';

const {JWT_PUBLIC_KEY} = process.env;
const UNAUTHORIZED = 'Unauthorized';
const FORBIDDEN = 'Forbidden';

export const ALLOWED_ROLES = [
  UserPermisssion.TEMPLATE_VIEW,
  UserPermisssion.TEMPLATE_CREATE,
  UserPermisssion.TEMPLATE_ARCHIVE,
  UserPermisssion.TEMPLATE_DRAFT_VIEW,
  UserPermisssion.RA_VIEW,
  UserPermisssion.RA_CREATE,
  UserPermisssion.RA_DRAFT_VIEW,
  UserPermisssion.RA_DRAFT_EDIT,
  UserPermisssion.RA_DRAFT_DISCARD,
  UserPermisssion.TEMPLATE_DRAFT_EDIT,
  UserPermisssion.TEMPLATE_DRAFT_DISCARD,
  UserPermisssion.MASTER_DATA_VIEW,
] as const;

export type AllowedRole = (typeof ALLOWED_ROLES)[number];

interface UserData extends APIGatewayAuthorizerResultContext {
  user_id?: string;
  user_name?: string;
  roles?: string;
  ship_party_id?: string;
  ship_party_type?: string;
  userAccess?: string;
  [key: string]: string | undefined;
}

interface CustomJWTPayload extends JwtPayload {
  user_id?: string;
  preferred_username?: string;
  realm_access?: {
    roles: string[];
  };
  group?: string[];
  ship_party_id?: string;
  ship_party_type?: string;
}

const generatePolicy = (
  userData: UserData,
  effect: 'Allow' | 'Deny',
  resource: string,
): APIGatewayAuthorizerResult => {
  const result = {
    context: userData,
    principalId: userData.user_id ?? userData.user_name ?? 'user',
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: effect,
          Resource: resource,
        },
      ],
    },
  };
  console.error('Generated policy:', JSON.stringify(result, null, 2));
  return result;
};

interface ExtendedAuthorizerEvent extends Record<string, any> {
  headers: Record<string, string>;
  methodArn?: string;
  routeArn?: string;
  requestContext?: {
    accountId: string;
    apiId: string;
    stage: string;
  };
}

export const authenticate = (
  event: ExtendedAuthorizerEvent,
  context: Context,
  callback: Callback<APIGatewayAuthorizerResult>,
): void => {
  try {
    const token = extractToken(event);
    if (!token) {
      console.error('No authorization token provided');
      return callback(UNAUTHORIZED);
    }

    verifyToken(token, event, callback);
  } catch (error) {
    console.error('Authentication error:', error);
    return callback(UNAUTHORIZED);
  }
};

// Helper to extract token
function extractToken(event: ExtendedAuthorizerEvent): string | null {
  const authHeader =
    event.headers?.authorization || event.headers?.Authorization;
  if (!authHeader) return null;

  const bearer = 'Bearer ';
  return authHeader.startsWith(bearer)
    ? authHeader.slice(bearer.length)
    : authHeader;
}

// Helper to verify and authorize token
function verifyToken(
  token: string,
  event: ExtendedAuthorizerEvent,
  callback: Callback<APIGatewayAuthorizerResult>,
): void {
  verify(
    token,
    JWT_PUBLIC_KEY!,
    {algorithms: process.env.JWT_PUBLIC_KEY ? ['RS256'] : ['HS256']},
    (error: Error | null, decoded: unknown) => {
      if (error || !decoded || typeof decoded === 'string') {
        return callback(UNAUTHORIZED);
      }

      const decodedPayload = decoded as CustomJWTPayload;
      const userRoles = decodedPayload.realm_access?.roles ?? [];
      // if (!hasAllowedRole(userRoles)) {
      //   console.error('User does not have required roles:', userRoles);
      //   return callback(FORBIDDEN);
      // }

      const userData = extractUserData(decodedPayload);
      const resource = determineResource(event);

      if (!resource) {
        console.error('No valid ARN or request context found in the event');
        return callback(UNAUTHORIZED);
      }

      console.error('Generated resource ARN:', resource);
      return callback(
        null,
        generatePolicy(
          omitBy(userData, isUndefined) as UserData,
          'Allow',
          resource,
        ),
      );
    },
  );
}

// Helper to check role
function hasAllowedRole(roles: string[]): boolean {
  return roles.some(role => ALLOWED_ROLES.includes(role as AllowedRole));
}

// Helper to extract user data
function extractUserData(decoded: CustomJWTPayload): UserData {
  return {
    user_id: decoded.user_id,
    user_name: decoded.preferred_username,
    roles: decoded.realm_access?.roles?.join(',') ?? '',
    ship_party_id: decoded.ship_party_id,
    ship_party_type: decoded.ship_party_type,
  };
}

// Helper to determine ARN
function determineResource(event: ExtendedAuthorizerEvent): string | null {
  if (event.methodArn) {
    return `${event.methodArn.split('/').slice(0, 2).join('/')}/*`;
  }

  if (event.routeArn) {
    return event.routeArn.split('/')[0] + '/*/*';
  }

  const ctx = event.requestContext;
  if (ctx?.accountId && ctx?.apiId) {
    return `arn:aws:execute-api:${process.env.AWS_REGION ?? 'us-east-1'}:${ctx.accountId}:${ctx.apiId}/${ctx.stage}/*`;
  }

  return null;
}

export default {authenticate};
