import {create<PERSON><PERSON><PERSON><PERSON>} from '../utils/lambda';
import {LambdaData, LambdaResponse} from '../types/lambda';
import ParameterTypeController from '../controller/parameter-type.controller';
import {RISK_DB} from '../db/db-client';
import {handleNotFoundError} from '../error';

/**
 * @swagger
 * /parameter-types:
 *   get:
 *     tags:
 *       - Parameter Types
 *     summary: Retrieve a list of parameter types
 *     description: Returns a filtered list of parameter types. Optional `search` parameter is used to filter results.

 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Text to search parameter types by name or description
 *         example: type1
 *       - in: query
 *         name: is_required_for_risk_rating
 *         schema:
 *           type: boolean
 *         description: Filter to show only required parameter types
 *         example: true
 *     responses:
 *       200:
 *         description: A list of matching parameter types
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 parameterTypes:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "Type 1"
 *                       description:
 *                         type: string
 *                         example: "Description of Type 1"
 *       401:
 *         description: Unauthorized (role missing or invalid token)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Unauthorized
 *       404:
 *         description: Path not found (for unsupported methods)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Path not found
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Internal error occurred
 */
export const main = createLambda(
  async (data: LambdaData): Promise<LambdaResponse> => {
    if (data.method !== 'GET') {
      return handleNotFoundError('Path not found');
    }
    try {
      const search = data.queryStringParameters?.search ?? '';
      const isRequired =
        data.queryStringParameters?.is_required_for_risk_rating === 'true';

      return {
        statusCode: 200,
        response: await ParameterTypeController.list(search, isRequired),
      };
    } catch (error: any) {
      console.error('Error handling parameter types request:', error);
      throw error;
    }
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => {
      return Promise.resolve();
    },
  },
);
