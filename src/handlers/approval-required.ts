import {handleNotFoundError} from '../error';
import {createLambda} from '../utils/lambda';
import ApprovalRequiredController from '../controller/approval-required.controller';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {RISK_DB} from '../db/db-client';

/**
 * @swagger
 * /approval-required:
 *   get:
 *     tags:
 *       - Approval Required
 *     summary: Retrieve a list of approval required roles
 *     description: Returns a filtered list of approval required roles. Optional `search` parameter is used to filter results.
 *
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Text to search approval required roles by name
 *         example:
 *     responses:
 *       200:
 *         description: A list of matching approval required roles
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 approvalRequired:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "Managing Directors"
 *       401:
 *         description: Unauthorized (role missing or invalid token)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Unauthorized
 *       404:
 *         description: Path not found (for unsupported methods)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Path not found
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Internal error occurred
 */

export const main = createLambda(
  async ({
    method,
    queryStringParameters,
  }: LambdaData): Promise<LambdaResponse> => {
    if (method !== 'GET') {
      return handleNotFoundError('Path not found');
    }

    const search = queryStringParameters?.search ?? '';
    return {
      statusCode: 200,
      response: await ApprovalRequiredController.list(search),
    };
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: ['nbp|insp|del'],
    validateAccess: async () => {
      return Promise.resolve();
    },
  },
);
