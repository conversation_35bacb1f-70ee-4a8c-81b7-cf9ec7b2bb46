import {handleNotFoundError} from '../error';
import {createLambda} from '../utils/lambda';
import HazardsController from '../controller/hazards.controller';
import {LambdaData, LambdaResponse} from '../types/lambda';
import {RISK_DB} from '../db/db-client';
import {UserPermisssion} from '../enums/permissions.enum';

/**
 * @swagger
 * /hazards:
 *   get:
 *     tags:
 *       - Hazards
 *     summary: Retrieve a list of hazards
 *     description: Returns a filtered list of hazards. Optional `search` parameter is used to filter results.

 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Text to search hazards by name or description
 *         example:
 *     responses:
 *       200:
 *         description: A list of matching hazards
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 hazards:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       name:
 *                         type: string
 *                         example: "Hazard Type"
 *                       description:
 *                         type: string
 *                         example: "List of Hzards used in risk assessment"
 *       401:
 *         description: Unauthorized (role missing or invalid token)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Unauthorized
 *       404:
 *         description: Path not found (for unsupported methods)
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Path not found
 *       500:
 *         description: Internal server error
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Internal error occurred
 */

export const main = createLambda(
  async ({
    method,
    queryStringParameters,
  }: LambdaData): Promise<LambdaResponse> => {
    if (method !== 'GET') {
      return handleNotFoundError('Path not found');
    }

    const search = queryStringParameters?.search ?? '';
    return {
      statusCode: 200,
      response: await HazardsController.list(search),
    };
  },
  {
    db: [RISK_DB],
    dataType: 'json',
    roles: [UserPermisssion.MASTER_DATA_VIEW],
    validateAccess: async () => {
      return Promise.resolve();
    },
  },
);
