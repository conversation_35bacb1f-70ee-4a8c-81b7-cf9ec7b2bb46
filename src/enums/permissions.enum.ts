export enum UserPermisssion {
  // Master Data
  MASTER_DATA_VIEW = 'ra|master|vw',
  // Template
  TEMPLATE_VIEW = 'tmpl|vw',
  TEMPLATE_CREATE = 'tmpl|add',
  TEMPLATE_ARCHIVE = 'tmpl|arch',
  TEMPLATE_DRAFT_VIEW = 'tmpl|draft|view',

  // Risks
  RA_VIEW = 'ra|view',
  RA_CREATE = 'ra|add',
  RA_DRAFT_VIEW = 'ra|draft|view',

  RISK_APPROVER_ADD = '',

  // RA Draft Actions
  RA_DRAFT_EDIT = 'ra|draft|edit',
  RA_DRAFT_DISCARD = 'ra|draft|discard',

  // Template Draft Actions
  TEMPLATE_DRAFT_EDIT = 'tmpl|draft|edt',
  TEMPLATE_DRAFT_DISCARD = 'tmpl|draft|dis',
}
