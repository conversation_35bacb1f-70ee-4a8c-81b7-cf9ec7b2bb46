{"name": "paris2-api-risk-assessment", "version": "1.0.0", "description": "Risk Assessment API", "scripts": {"dev": "nodemon", "build": "tsc", "test": "DB_URI=sqlite::memory: mocha --r ts-node/register ./src/__tests__/**/*.spec.ts", "test:watch": "DB_URI=sqlite::memory: mocha --r ts-node/register --watch ./src/__tests__/**/*.spec.ts", "test:coverage": "DB_URI=sqlite::memory: nyc mocha --r ts-node/register ./src/__tests__/**/*.spec.ts", "prepare": "husky install", "env:load": "ts-node --esm -T load-env-var.js", "prettier:cli": "prettier", "prettier:check": "npm run prettier:cli -- --check \"src/**/*.ts\"", "prettier:fix": "npm run prettier:cli -- --write \"src/**/*.ts\"", "swagger:build": "ts-node generate-swagger.ts", "swagger:start": "npm run swagger:build && ts-node swagger-server.ts"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.0.0", "@types/swagger-jsdoc": "^6.0.4", "axios": "^1.9.0", "busboy": "^1.6.0", "chai-as-promised": "^8.0.1", "dotenv": "^16.5.0", "express": "^5.1.0", "husky": "^9.1.7", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "qs": "^6.14.0", "sequelize": "^6.37.7", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1"}, "devDependencies": {"@aws-sdk/client-ssm": "^3.806.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/aws-lambda": "^8.10.134", "@types/busboy": "^1.5.3", "@types/chai": "^4.3.7", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.14.202", "@types/mocha": "^10.0.10", "@types/node": "^20.11.24", "@types/sequelize": "^4.28.20", "@types/sinon": "^17.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^9.0.8", "chai": "^4.5.0", "commitizen": "^4.3.1", "cz-customizable": "^7.4.0", "mocha": "^11.5.0", "nodemon": "^3.1.10", "nyc": "^17.1.0", "prettier": "^3.5.3", "serverless": "^4.14.3", "serverless-offline": "^14.4.0", "sinon": "^20.0.0", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "swagger-jsdoc": "^6.2.8", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": ">=20.0.0"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}}