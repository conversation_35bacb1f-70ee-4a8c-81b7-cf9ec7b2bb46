{"compilerOptions": {"esModuleInterop": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "moduleResolution": "node", "rootDir": "./", "outDir": ".build", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"*": ["node_modules/*", "src/types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules/**/*", ".serverless/**/*", ".webpack/**/*", "_warmup/**/*", ".vscode/**/*"]}