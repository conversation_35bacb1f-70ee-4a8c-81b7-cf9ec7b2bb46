#!/bin/bash
export ENV=${ENV:-dev}
export REGION=${REGION:-ap-southeast-1}
export ACCOUNT=${ACCOUNT:-************}
echo ${REGION}
echo ${ACCOUNT}

source ./load-env-vars.sh

# Build dependencies layer
echo "Building dependencies layer..."
cd layer/nodejs && npm install --production --no-optional && cd ../..

# Deploy functions individually with optimized packages
echo "Starting function deployment..."

NODE_OPTIONS="--max-old-space-size=4096" npx serverless deploy -s $ENV --region ${REGION} --param="account=${ACCOUNT}"
echo "All functions deployed successfully!"

# deploy swagger
export ENV=${ENV:-dev}
npm run swagger:build && aws s3 cp openapi.json s3://paris2-static-$ENV/swagger/$ENV/swagger-risk-assessment.json --acl public-read