{"openapi": "3.0.3", "info": {"title": "Risk Assessment API", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000/stage", "variables": {}}], "paths": {"/approval-required": {"get": {"tags": ["Approval Required"], "summary": "Retrieve a list of approval required roles", "description": "Returns a filtered list of approval required roles. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search approval required roles by name", "example": null}], "responses": {"200": {"description": "A list of matching approval required roles", "content": {"application/json": {"schema": {"type": "object", "properties": {"approvalRequired": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Managing Directors"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/categories": {"get": {"tags": ["Category"], "summary": "Retrieve a list of categories", "description": "Returns a filtered list of categories. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search categories by name or description", "example": "vessel"}], "responses": {"200": {"description": "A list of matching categories", "content": {"application/json": {"schema": {"type": "object", "properties": {"categories": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Vessel Type"}, "description": {"type": "string", "example": "List of vessel types used in risk assessment"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/hazards": {"get": {"tags": ["Hazards"], "summary": "Retrieve a list of hazards", "description": "Returns a filtered list of hazards. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search hazards by name or description", "example": null}], "responses": {"200": {"description": "A list of matching hazards", "content": {"application/json": {"schema": {"type": "object", "properties": {"hazards": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Hazard Type"}, "description": {"type": "string", "example": "List of Hzards used in risk assessment"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/parameter-types": {"get": {"tags": ["Parameter Types"], "summary": "Retrieve a list of parameter types", "description": "Returns a filtered list of parameter types. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search parameter types by name or description", "example": "type1"}, {"in": "query", "name": "is_required_for_risk_rating", "schema": {"type": "boolean"}, "description": "Filter to show only required parameter types", "example": true}], "responses": {"200": {"description": "A list of matching parameter types", "content": {"application/json": {"schema": {"type": "object", "properties": {"parameterTypes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Type 1"}, "description": {"type": "string", "example": "Description of Type 1"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/parameters": {"get": {"tags": ["Parameters"], "summary": "Retrieve a list of parameters", "description": "Returns a filtered list of parameters. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search parameters by name or description", "example": "parameter1"}], "responses": {"200": {"description": "A list of matching parameters", "content": {"application/json": {"schema": {"type": "object", "properties": {"parameters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Parameter 1"}, "description": {"type": "string", "example": "Description of Parameter 1"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/vessel-ownership": {"get": {"tags": ["Paris2"], "summary": "Retrieve a list of vessel ownerships", "description": "Returns a filtered list of vessel ownerships. Optional `keyword` parameter is used to filter results.", "parameters": [{"in": "query", "name": "keyword", "schema": {"type": "string"}, "description": "Text to search vessel ownerships by keyword"}], "responses": {"200": {"description": "A list of matching vessel ownerships", "content": {"application/json": {"schema": {"type": "object", "properties": {"vesselOwnerships": {"type": "array"}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/crew-list": {"get": {"tags": ["Paris2"], "summary": "Retrieve crew list for a vessel", "description": "Returns the crew list for a specific vessel and month. If month is not provided, returns data for current month.", "parameters": [{"in": "query", "name": "vessel_id", "required": true, "schema": {"type": "integer"}, "description": "ID of the vessel to get crew list for"}, {"in": "query", "name": "month", "schema": {"type": "string", "pattern": "^\\d{4}-\\d{2}$"}, "description": "Month in YYYY-MM format (optional, defaults to current month)"}], "responses": {"200": {"description": "Successfully retrieved crew list", "content": {"application/json": {"schema": {"type": "object", "properties": {"crewList": {"type": "array"}}}}}}, "400": {"description": "Bad request (missing or invalid vessel_id)", "content": {"text/plain": {"schema": {"type": "string", "example": "vessel_id is required"}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/reporting-office": {"get": {"tags": ["Paris2"], "summary": "Retrieve a list of reporting offices", "description": "Returns all reporting offices from the paris2 API", "responses": {"200": {"description": "Successfully retrieved reporting offices", "content": {"application/json": {"schema": {"type": "object", "properties": {"reportingOffices": {"type": "array"}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/risk-approver": {"post": {"tags": ["Risk Approver"], "summary": "Add approvers to a risk assessment", "description": "Adds one or more approvers to a risk assessment based on the RA level", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["risk_id", "ra_level", "keycloak_id"], "properties": {"risk_id": {"type": "number", "description": "ID of the risk assessment"}, "ra_level": {"type": "number", "enum": [1, 2, 3], "description": "Risk Assessment level (1=Low, 2=Medium, 3=High)"}, "keycloak_id": {"type": "array", "items": {"type": "number"}, "description": "Array of approver keycloak IDs (1 approver for level 1, 3 approvers for level 2 and 3)"}}}}}}, "responses": {"200": {"description": "Risk approvers added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Risk approvers added successfully"}, "risk_id": {"type": "number", "example": 1}, "ra_level": {"type": "number", "example": 2}}}}}}, "400": {"description": "Invalid request body or validation error"}, "404": {"description": "Risk assessment not found"}}}}, "/risk-approver/{risk_id}/status": {"put": {"tags": ["Risk Approver"], "summary": "Update risk approver status", "description": "Update the approval status and optional message for a risk approver", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "risk_id", "required": true, "schema": {"type": "number"}, "description": "ID of the risk assessment"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["status"], "properties": {"status": {"type": "number", "enum": [1, 2, 3], "description": "Approval status (1=APPROVED, 2=REJECTED, 3=CONDITIONALLY_APPROVED)"}, "message": {"type": "string", "description": "Required for REJECTED and CONDITIONALLY_APPROVED status"}}}}}}, "responses": {"200": {"description": "Risk approver status updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Risk approver status updated successfully"}, "risk_id": {"type": "number", "example": 1}, "approval_status": {"type": "number", "example": 1}}}}}}, "400": {"description": "Invalid request body, invalid status, or missing required message"}, "404": {"description": "Risk approver not found"}}}}, "/risk-approver/{risk_id}": {"put": {"tags": ["Risk Approver"], "summary": "Update risk approvers and RA level", "description": "Update the list of risk approvers and RA level for a risk assessment", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "risk_id", "required": true, "schema": {"type": "number"}, "description": "ID of the risk assessment"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["ra_level", "approvers"], "properties": {"ra_level": {"type": "string", "enum": ["ROUTINE", "SPECIAL", "CRITICAL"], "description": "Risk Assessment level"}, "approvers": {"type": "array", "items": {"type": "number"}, "description": "Array of approver keycloak IDs"}}}}}}, "responses": {"200": {"description": "Risk approvers updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Risk approvers and RA level updated successfully"}, "risk_id": {"type": "number", "example": 1}, "ra_level": {"type": "string", "example": "SPECIAL"}}}}}}, "400": {"description": "Invalid request body or validation error"}, "404": {"description": "Risk assessment not found"}}}}, "/risks/{risk_id}/team-members": {"put": {"tags": ["Risk"], "summary": "Update team members for a risk", "description": "Updates the list of team members associated with a risk", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "risk_id", "required": true, "schema": {"type": "integer"}, "description": "ID of the risk"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"team_members": {"type": "array", "items": {"type": "object", "properties": {"seafarer_id": {"type": "integer"}, "seafarer_person_id": {"type": "integer"}, "seafarer_hkid": {"type": "integer"}, "seafarer_name": {"type": "string"}, "seafarer_rank": {"type": "string"}, "seafarer_rank_id": {"type": "integer"}, "seafarer_rank_sort_order": {"type": "string"}}}}}}}}}, "responses": {"200": {"description": "Team members updated successfully"}, "400": {"description": "Validation error"}, "404": {"description": "Risk not found"}, "500": {"description": "Internal server error"}}}}, "/risks": {"post": {"tags": ["Risk"], "summary": "Create a new risk assessment", "description": "Creates a new risk assessment with the provided details.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"template_id": {"type": "integer", "description": "ID of the template to base the risk assessment on"}, "task_requiring_ra": {"type": "string", "description": "Task requiring risk assessment"}, "assessor": {"type": "integer", "description": "ID of the assessor"}, "vessel_ownership_id": {"type": "integer", "description": "ID of the vessel ownership"}, "date_risk_assessment": {"type": "string", "format": "date", "description": "Date of risk assessment"}, "task_duration": {"type": "integer", "description": "Duration of the task"}, "ra_level": {"type": "string", "enum": ["LEVEL1", "LEVEL2", "LEVEL3"], "description": "Risk assessment level"}}}}}}, "responses": {"201": {"description": "Risk assessment created successfully"}, "400": {"description": "Validation error"}, "500": {"description": "Internal server error"}}}, "get": {"tags": ["Risk"], "summary": "Get a list of risk assessments", "description": "Retrieves a paginated list of risk assessments with optional filters.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Search term to filter risk assessments"}, {"in": "query", "name": "vessel_name", "schema": {"type": "string"}, "description": "Filter by vessel name"}, {"in": "query", "name": "office_name", "schema": {"type": "string"}, "description": "Filter by office name"}, {"in": "query", "name": "vessel_category", "schema": {"type": "string"}, "description": "Filter by vessel category"}, {"in": "query", "name": "ra_level", "schema": {"type": "string", "enum": ["LEVEL1", "LEVEL2", "LEVEL3"]}, "description": "Filter by risk assessment level"}, {"in": "query", "name": "status", "schema": {"type": "string"}, "description": "Filter by status (e.g., DRAFT, PUBLISHED)"}, {"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of risk assessments retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Risk"}}, "pagination": {"type": "object", "properties": {"totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/risks/{id}": {"patch": {"tags": ["Risk"], "summary": "Update an existing risk assessment", "description": "Updates an existing risk assessment with the provided details.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the risk assessment to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Risk"}}}}, "responses": {"200": {"description": "Risk assessment updated successfully"}, "400": {"description": "Validation error"}, "404": {"description": "Risk assessment not found"}, "500": {"description": "Internal server error"}}}, "get": {"tags": ["Risk"], "summary": "Get a risk assessment by ID", "description": "Retrieves a risk assessment by its ID with all associated data.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the risk assessment to retrieve"}], "responses": {"200": {"description": "Risk assessment retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Risk"}}}}, "404": {"description": "Risk assessment not found"}, "500": {"description": "Internal server error"}}}, "delete": {"tags": ["Risk"], "summary": "Delete a risk assessment", "description": "Deletes a risk assessment by its ID. Only risk assessments with DRAFT status can be deleted.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the risk assessment to delete"}], "responses": {"200": {"description": "Risk assessment deleted successfully"}, "400": {"description": "Cannot delete non-draft risk assessment"}, "404": {"description": "Risk assessment not found"}, "500": {"description": "Internal server error"}}}}, "/risks/options/{key}": {"get": {"tags": ["Risk"], "summary": "Get distinct options for a risk field", "description": "Returns a list of distinct values for a specified risk field (vessel_name, vessel_category, vessel_tech_group, or office_name).", "parameters": [{"in": "path", "name": "key", "required": true, "schema": {"type": "string", "enum": ["vessel_category", "vessel_tech_group"]}, "description": "The field to get options for"}], "responses": {"200": {"description": "List of distinct options for the given key", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"type": "object"}}}}}}}, "400": {"description": "Validation error"}, "500": {"description": "Internal server error"}}}}, "/task-reliability-assessments": {"get": {"tags": ["Task Reliability Assessments"], "summary": "Retrieve a list of task reliability assessments", "description": "Returns a filtered list of task reliability assessments. Optional `search` parameter is used to filter results.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Text to search task reliability assessments by name or description", "example": "assessment1"}], "responses": {"200": {"description": "A list of matching task reliability assessments", "content": {"application/json": {"schema": {"type": "object", "properties": {"taskReliabilityAssessments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Assessment 1"}, "description": {"type": "string", "example": "Description of Assessment 1"}}}}}}}}}, "401": {"description": "Unauthorized (role missing or invalid token)", "content": {"text/plain": {"schema": {"type": "string", "example": "Unauthorized"}}}}, "404": {"description": "Path not found (for unsupported methods)", "content": {"text/plain": {"schema": {"type": "string", "example": "Path not found"}}}}, "500": {"description": "Internal server error", "content": {"text/plain": {"schema": {"type": "string", "example": "Internal error occurred"}}}}}}}, "/templates": {"post": {"tags": ["Template"], "summary": "Create a new template", "description": "Creates a new template with the provided details.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"task_requiring_ra": {"type": "string", "description": "Task requiring risk assessment"}, "task_duration": {"type": "integer", "description": "Duration of the task"}, "task_duration_unit": {"type": "string", "description": "Unit of task duration"}, "template_category": {"type": "object", "properties": {"category_id": {"type": "array", "items": {"type": "integer"}}}, "description": "Categories associated with the template"}, "template_hazard": {"type": "object", "properties": {"hazard_id": {"type": "array", "items": {"type": "integer"}}, "is_other": {"type": "boolean"}, "value": {"type": "string"}}, "description": "Hazards associated with the template"}, "parameters": {"type": "array", "items": {"type": "object", "properties": {"parameter_id": {"type": "array", "items": {"type": "integer"}}, "parameter_type_id": {"type": "integer"}, "is_other": {"type": "boolean"}, "value": {"type": "string"}}}, "description": "Parameters associated with the template"}}}}}}, "responses": {"201": {"description": "Template created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "400": {"description": "Validation error"}, "500": {"description": "Internal server error"}}}, "get": {"tags": ["Template"], "summary": "Get a list of templates", "description": "Retrieves a list of templates with optional filters.", "parameters": [{"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Search term to filter templates"}, {"in": "query", "name": "sort_by", "schema": {"type": "string"}, "description": "Field to sort by"}, {"in": "query", "name": "sort_order", "schema": {"type": "string", "enum": ["ASC", "DESC"]}, "description": "Sort order"}, {"in": "query", "name": "created_by", "schema": {"type": "array", "items": {"type": "string"}}, "style": "form", "explode": true, "description": "Filter by creator IDs"}, {"in": "query", "name": "created_by[start_date]", "schema": {"type": "string", "format": "date"}, "description": "Filter by start date of creation"}, {"in": "query", "name": "created_by[end_date]", "schema": {"type": "string", "format": "date"}, "description": "Filter by end date of creation"}, {"in": "query", "name": "ra_categories", "schema": {"type": "array", "items": {"type": "integer"}}, "style": "form", "explode": true, "description": "Filter by risk assessment category IDs"}, {"in": "query", "name": "hazard_categories", "schema": {"type": "array", "items": {"type": "integer"}}, "style": "form", "explode": true, "description": "Filter by hazard category IDs"}, {"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of templates retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "pagination": {"type": "object", "properties": {"totalItems": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/templates/{id}": {"patch": {"tags": ["Template"], "summary": "Update an existing template", "description": "Updates an existing template with the provided details.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the template to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "responses": {"200": {"description": "Template updated successfully"}, "400": {"description": "Validation error"}, "404": {"description": "Temp<PERSON> not found"}, "500": {"description": "Internal server error"}}}, "get": {"tags": ["Template"], "summary": "Get a template by ID", "description": "Retrieves a template by its ID.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the template to retrieve"}], "responses": {"200": {"description": "Template retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "404": {"description": "Temp<PERSON> not found"}, "500": {"description": "Internal server error"}}}, "delete": {"tags": ["Template"], "summary": "Delete a template", "description": "Deletes a template by its ID.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the template to delete"}], "responses": {"200": {"description": "Template deleted successfully"}, "404": {"description": "Temp<PERSON> not found"}, "500": {"description": "Internal server error"}}}}, "/templates/{id}/inactive": {"patch": {"tags": ["Template"], "summary": "Mark a template and related tables as inactive", "description": "Updates the status of a template and its related tables to inactive.", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the template to mark as inactive"}], "responses": {"200": {"description": "Template marked as inactive successfully"}, "404": {"description": "Temp<PERSON> not found"}, "500": {"description": "Internal server error"}}}}, "/templates/users": {"get": {"tags": ["Template"], "summary": "Get all unique users who created templates", "description": "Retrieves all unique user IDs from template creators and their details", "responses": {"200": {"description": "List of unique users retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "result": {"type": "array", "items": {"type": "object", "properties": {"userId": {"type": "string"}, "email": {"type": "string"}}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/templates/top": {"get": {"tags": ["Template"], "summary": "Get top templates by risk count", "description": "Retrieves the top templates based on the count of associated risks.", "parameters": [{"in": "query", "name": "maxCount", "schema": {"type": "integer"}, "required": true, "description": "Maximum number of results to return"}], "responses": {"200": {"description": "List of top templates", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "template_count": {"type": "integer"}}}}}}}, "400": {"description": "Validation error"}, "500": {"description": "Internal server error"}}}}}, "components": {"schemas": {}, "securitySchemes": {"keycloak": {"type": "oauth2", "description": "keycloak oauth", "flows": {"implicit": {"authorizationUrl": "http://localhost:3000/stage", "scopes": {}}}}}}, "security": [{"keycloak": []}], "tags": []}