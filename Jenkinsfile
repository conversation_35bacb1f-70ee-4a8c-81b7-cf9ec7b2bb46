final ENV_GIT_BRANCH    = env.BRANCH
final ENV_STAGE_NAME    = env.stageName
final ENV_SKIP_TESTS    = env.skipTests
final ENV_SKIP_BUILD    = env.skipBuild
final ENV_SOURCE_BRANCH = env.sourcebranch
final ENV_TARGET_BRANCH = env.targetbranch
final ENV_STATE         = env.state
final ENV_GIT_REPO = env.gitRepo
final ENV_REGION        = env.region
ENV_ACCOUNT_ID    = env.account
final ENV_CONFIG_BRANCH = env.configBranch

def stageName           = ENV_STAGE_NAME
def region              = ENV_REGION
def accountId               = ENV_ACCOUNT_ID
def autoAnalysisBranch  = env.autoAnalysisBranch ?: 'release'


def nodeLabel = env.nodeLabel ?: 'fleet-slave-node22'
def dockerImage = env.dockerImage ?: '${accountId}.dkr.ecr.${region}.amazonaws.com/jenkins-build-agent:node18'
def testCommand = env.testCommand ?: './test.sh'
def buildCommand = env.buildCommand ?: './build.sh'
def codeAnalysisCommand = env.codeAnalysisCommand ?: "./upload_sonar_report.sh"
def deployCommand = "ENV=${stageName} REGION=${region} ACCOUNT=${accountId} ./deploy.sh"
def loadEnvCommand = "ENV=${stageName} REGION=${region} ACCOUNT=${accountId} MAKE_ENV_FILE=true ./load-env-vars.sh"
def migrationCommand = "ENV=${stageName} REGION=${region} ACCOUNT=${accountId} MAKE_ENV_FILE=true ./migration.sh"
def timeZone = env.timeZone ?: 'Asia/Hong_Kong'
def shouldPullDockerImage = env.shouldPullDockerImage == 'true'
def shouldBuild =  env.shouldSkipBuild != 'true'
def configBranch = env.configBranch ?: 'master'
def releaseVersionId = env.releaseVersionId 
def shouldRunCodeAnalysis = env.shouldRunCodeAnalysis == 'true'
def shouldRunMigration = env.shouldRunMigration == 'true'

def shouldRelease = false
def shouldTest = ENV_SKIP_TESTS != 'true'
echo "ENV_GIT_BRANCH = ${ENV_GIT_BRANCH}"
echo 'pull request info:'
echo "ENV_SOURCE_BRANCH = ${ENV_SOURCE_BRANCH}"
echo "ENV_TARGET_BRANCH = ${ENV_TARGET_BRANCH}"
echo "ENV_STATE = ${ENV_STATE}"
echo "ENV_RELEASE_VERSION_ID = ${releaseVersionId}"

def ssh_private_key = ''

// Test action condition
if (ENV_SOURCE_BRANCH == 'develop' && ENV_TARGET_BRANCH == 'master') {
  shouldTest = false
}

echo "shouldTest = ${shouldTest}"

// Release action condition
if (stageName) {
  echo "Triggered to release to stage: ${stageName}"
  shouldRelease = true
  if (stageName == 'stage') {
    nodeLabel = 'fleet-slave'
  }
} else if (ENV_STATE == 'MERGED' && ENV_TARGET_BRANCH == 'develop') {
  echo "Triggered by pull request merged event, will be released to DEV"
  stageName = 'dev2'
  shouldRelease = true
} else {
  stageName = 'dev2'
  shouldTest = true
  echo "No build will be released"
}

if (ENV_STATE == 'MERGED' || env.shouldRunCodeAnalysis == 'true') {
  shouldRunCodeAnalysis = true
}

if ( ENV_TARGET_BRANCH == autoAnalysisBranch)
{
  shouldRunCodeAnalysis = true
}

echo "shouldRunCodeAnalysis = ${shouldRunCodeAnalysis}"

echo "stageName = ${stageName}"
echo "shouldRelease = ${shouldRelease}"

def aws(handle) {
  if (stageName == "live") {
    withAWS(roleAccount: "${ENV_ACCOUNT_ID}", role:'paris2-live-cross-account-access'){
      handle()
    }
  } else {
      withCredentials([[
        $class: 'AmazonWebServicesCredentialsBinding',
        credentialsId: "paris2_jenkins",
        accessKeyVariable: 'AWS_ACCESS_KEY_ID',
        secretKeyVariable: 'AWS_SECRET_ACCESS_KEY'
      ]]){
        handle()
    }
  }
}

def notifyBitbucket(stateName, onStage) {
  bitbucketStatusNotify(
    buildState: 'INPROGRESS',
    buildKey: stateName,
    buildName: stateName
  )

  try {
    onStage()
    bitbucketStatusNotify(
      buildState: 'SUCCESSFUL',
      buildKey: stateName,
      buildName: stateName
    )
  } catch (exc) {
    bitbucketStatusNotify(
      buildState: 'FAILED',
      buildKey: stateName,
      buildName: stateName,
      buildDescription: "${stateName} failed!!"
    )
    /* Rethrow to fail the Pipeline properly */
    throw exc
  }
}

def silent_sh(cmd) {
    sh('#!/bin/sh -e\n' + cmd)
}

pipeline {
  agent {
    label "${nodeLabel}"
  }
  options {
        // Increase build timeout
        timeout(time: 30, unit: 'MINUTES')
  }

  environment {
    IS_PR_WEBHOOK = false // Default to false
    IMAGE_TAG_VERSION="${stageName}-${env.BUILD_NUMBER}"
    IMAGE_REPO_NAME="${accountId}.dkr.ecr.${region}.amazonaws.com/paris2-risk-assessment-${stageName}"
    DOCKER_BUILDKIT="1"
  }

  stages {
    stage('Check if Triggered by PR Webhook') {
      steps {
        script {
          // Detect if the pipeline was triggered by a PR webhook
          if (ENV_STATE == 'OPEN' && ENV_SOURCE_BRANCH && ENV_TARGET_BRANCH) {
            echo "Pipeline triggered by a PR webhook."
            IS_PR_WEBHOOK = true
            shouldTest = true
            shouldRunCodeAnalysis = true
            shouldBuild = true
            shouldRelease = false
          } else {
            echo "Pipeline was not triggered by a PR webhook."
          }
        }
      }
    }

    stage('Check Branch Name') {
      steps {
        script {
          if (stageName == "live") {
            // Check if ENV_GIT_BRANCH starts with refs/tags/
            if (!ENV_GIT_BRANCH.startsWith('refs/tags/')) {
              error("Deployment can only be triggered with tags. Please use tags for deployment.")
            }
          }
        }
      }
    }

    stage('Checkout') {
      steps {
        notifyBitbucket('Checkout') {
          checkout([
            $class: 'GitSCM',
            branches: [[name: "${ENV_GIT_BRANCH}"]],
            doGenerateSubmoduleConfigurations: false,
            extensions: [], submoduleCfg: [],
            userRemoteConfigs: [[
              name: 'github',
              credentialsId: 'fleet_devops',
              url: "https://<EMAIL>/fleetshipteam/paris2-api-risk-assessment.git"
            ]]
          ])
        }

        script {
          aws {
            sh "aws ecr get-login-password --region ${region} | sudo docker login --username AWS --password-stdin ${accountId}.dkr.ecr.${region}.amazonaws.com"
            ssh_private_key = readFile(file: '/home/<USER>/.ssh/id_rsa')
          }
        }
      }
    }

    stage('Check Tag on Branch') {
      steps {
          script {
              if (stageName == "live") {
                  sh "sudo git checkout master"
                  def branch = 'master'
                  
                  def result = sh(script: "git branch --contains \$(git rev-parse ${ENV_GIT_BRANCH}) | grep '^\\* ${branch}\$'", returnStatus: true)
                  
                  if (result == 0) {
                      echo "${ENV_GIT_BRANCH} is contained within ${branch}"
                      sh "sudo git checkout ${ENV_GIT_BRANCH}"
                  } else {
                      error("${ENV_GIT_BRANCH} is not contained within ${branch}")
                  }
              } else {
                  echo "Skipping stage check Tag on Branch for ${stageName} "
                }
            }
        }
    }


    stage('Load Configuration') {
      steps{
        sh "chmod +x ./load-configuration.sh"
        withCredentials([usernamePassword(credentialsId: 'paris2-configuration-bitbucket-app-password', usernameVariable: 'BITBUCKET_USER_NAME', passwordVariable: 'BITBUCKET_APP_PASSWORD')]) {
          sh "ENV=${stageName} CONFIG_BRANCH=${configBranch} ./load-configuration.sh"
          sh "cat paris2-configuration.json"
        }
      }
    }    


    stage('Pull docker image') {
      steps {
        timeout(time: 3, unit: 'MINUTES'){
          script {
            aws {
              sh "sudo docker pull ${dockerImage}"
            }
          }
        }
      }
    }

    stage('Test and deploy in container') {
      agent {
        docker {
          image "${dockerImage}"
          reuseNode true
          args "--net=host -v /run/dbus/system_bus_socket:/run/dbus/system_bus_socket:ro"
        }
      }

      environment {
        NPM_TOKEN = credentials('npmToken')
        SSH_PRIVATE_KEY = readFile(file: '/home/<USER>/.ssh/id_rsa')
        SERVERLESS_ACCESS_KEY = credentials('serverlessToken')
        TIME_ZONE = "${timeZone}"
        ENV="${stageName}"
        ACCOUNT="${accountId}"
        REGION="${region}"
        CONFIG_BRANCH="${configBranch}"
      }

      stages {
        stage('Install Environment and build') {
          when {
            expression {
              return shouldBuild;
            }
          }
          environment {
            ENV="${stageName}"
          }
          steps {
            timeout(time: 30, unit: 'MINUTES') {
              script {
                notifyBitbucket('Build') {
                  silent_sh "echo \"${SSH_PRIVATE_KEY}\" > \$HOME/.ssh/id_rsa"
                  sh "chmod 600 \$HOME/.ssh/id_rsa"
                  aws {
                    sh "${buildCommand}"
                  }
                }
              }
            }
          }
        }


        stage('Test') {
          when {
            expression {
              return shouldTest;
            }
          }

          steps {
            timeout(time: 20, unit: 'MINUTES'){
              script {
                notifyBitbucket('Test') {
                  sh "TZ=${TIME_ZONE} ${testCommand}"
                }
              }
            }
          }
        }

        stage('Sonar analysis') {
                    
          when {
            expression {
              return shouldRunCodeAnalysis;
            }
          }

          steps {
            script {
              echo 'Running Code Analysis stage'

              def runAnalysis = { scriptPath ->
                sh "${scriptPath}"

                // Fetch SonarQube quality gate status
                def SONAR_AUTH_TOKEN = sh(
                  script: "aws ssm get-parameter --name '/paris2-sonar-auth-token/${stageName}' --with-decryption --query 'Parameter.Value' --output text",
                  returnStdout: true
                ).trim()

                echo "Sonar Token fetched: ${SONAR_AUTH_TOKEN}"

                def QUALITY_GATE_STATUS = sh(
                  script: """curl -s -u ${SONAR_AUTH_TOKEN}: \
                  "https://sonar-dev.fleetship.com/api/qualitygates/project_status?projectKey=paris2-api-risk-assessment" \
                  | jq -r '.projectStatus.status'""",
                  returnStdout: true
                ).trim()

                echo "Quality Gate Status: ${QUALITY_GATE_STATUS}"

                if ("${QUALITY_GATE_STATUS}" != "OK") {
                  echo "SonarQube quality gate failed!"
                  error "Pipeline aborted due to SonarQube quality gate failure: ${QUALITY_GATE_STATUS}"
                } else {
                  echo "SonarQube quality gate passed!"
                  notifyBitbucket('Sonar analysis Passed') {
                    echo "Reporting to Bitbucket..."
                  }
                }
              }

              aws {
                withCredentials([usernamePassword(credentialsId: 'paris2-configuration-bitbucket-app-password', usernameVariable: 'BITBUCKET_USER_NAME', passwordVariable: 'BITBUCKET_APP_PASSWORD')]) {
                  try {
                    if (codeAnalysisCommand == "./upload_sonar_report.sh") {
                      echo 'Checking ./upload_sonar_report.sh'
                      def isCodeAnalysisScriptExist = sh script: 'test -f ./upload_sonar_report.sh', returnStatus: true
                      
                      echo "isCodeAnalysisScriptExist: ${isCodeAnalysisScriptExist}"
                      if (isCodeAnalysisScriptExist == 0) {
                        runAnalysis("./upload_sonar_report.sh")
                      } else {
                        echo 'No upload_sonar_report.sh found, skipping it'
                      }
                    } else {
                      echo 'Running codeAnalysisCommand directly'
                      runAnalysis(codeAnalysisCommand)
                    }
                  } catch (Exception e) {
                    bitbucketStatusNotify(buildState: 'FAILED')
                    error "Pipeline aborted due to SonarQube quality gate failure"
                  }
                }
              }
            }
          }
        }

        stage('Run DB Migration') {
          when {
            expression {
              return shouldRunMigration
            }
          }

          environment {
            NPM_TOKEN = credentials('npmToken')
            TIME_ZONE = "${timeZone}"
            ENV="${stageName}"
            ACCOUNT_ID="${accountId}"
          }


          steps {
            timeout(time: 15, unit: 'MINUTES'){
              script {
                notifyBitbucket('Migration') {
                  aws{
                    withCredentials([usernamePassword(credentialsId: 'paris2-configuration-bitbucket-app-password', usernameVariable: 'BITBUCKET_USER_NAME', passwordVariable: 'BITBUCKET_APP_PASSWORD')]) {
                      sh "npm config fix"
                      sh "${migrationCommand}"
                    }
                  }
                }
              }
            }
          }
        }

        stage('Deploy') {
          when {
            expression {
              return shouldRelease
            }
          }

          environment {
            NPM_TOKEN = credentials('npmToken')
            TIME_ZONE = "${timeZone}"
            SERVERLESS_ACCESS_KEY = credentials('serverlessToken')
            ENV="${stageName}"
            ACCOUNT_ID="${accountId}"
          }


          steps {
            timeout(time: 30, unit: 'MINUTES'){
              script {
                notifyBitbucket('Deploy') {
                  aws{
                    withCredentials([usernamePassword(credentialsId: 'paris2-configuration-bitbucket-app-password', usernameVariable: 'BITBUCKET_USER_NAME', passwordVariable: 'BITBUCKET_APP_PASSWORD')]) {
                      sh "npm config fix"
                      withCredentials([string(credentialsId: 'serverlessToken', variable: 'SERVERLESS_ACCESS_KEY')]) {
                        sh "${deployCommand}"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  post {
    always {
      cleanWs()
    }
  }
}
