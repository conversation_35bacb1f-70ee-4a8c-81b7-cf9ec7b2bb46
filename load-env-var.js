const {SSMClient, GetParameterCommand} = require('@aws-sdk/client-ssm');

const getSecret = async (key, awsRegion, profile) => {
  const config = {
    region: awsRegion,
  };
  if (profile) config.profile = profile;
  const ssmClient = new SSMClient(config);
  const response = await ssmClient.send(
    new GetParameterCommand({
      Name: key,
      WithDecryption: true,
    }),
  );
  return response?.Parameter?.Value || null;
};

(async () => {
  const keyName = process.argv[2];
  const awsRegion = process.argv[3] || 'ap-southeast-1';
  const profile = process.argv[4];
  const value = await getSecret(keyName, awsRegion, profile);
  console.log(value); // echo to the console
})();
