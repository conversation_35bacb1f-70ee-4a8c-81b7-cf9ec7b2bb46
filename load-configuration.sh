#!/bin/bash

# this script is mainly for <PERSON> to download the file
# You can checkout https://bitbucket.org/fleetshipteam/paris2-configuration for local development

COMMIT_ID=$(curl -s --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/commits/$CONFIG_BRANCH?limit=1" | jq -r '.values[0].hash')
curl -s -S --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD -L -O "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/src/${COMMIT_ID}/$ENV/paris2-configuration.json"