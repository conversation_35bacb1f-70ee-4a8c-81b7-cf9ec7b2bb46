# AWS Lambda Layers: Complete Guide

## Table of Contents
1. [What Are Lambda Layers?](#what-are-lambda-layers)
2. [The Problem We Solved](#the-problem-we-solved)
3. [How Layers Work](#how-layers-work)
4. [Implementation Details](#implementation-details)
5. [Configuration Examples](#configuration-examples)
6. [Benefits & Performance](#benefits--performance)
7. [Best Practices](#best-practices)

## What Are Lambda Layers?

Lambda Layers are a way to **share code and dependencies** across multiple Lambda functions without including them in each function's deployment package. Think of layers as shared libraries that get mounted to your Lambda functions at runtime.

## The Problem We Solved

### Before: Traditional Packaging
```
Function Package = Your Code + ALL Dependencies
├── src/handlers/auth.js
├── src/handlers/categories.js  
├── node_modules/
│   ├── pg/ (15MB)
│   ├── sequelize/ (25MB)
│   ├── axios/ (5MB)
│   ├── lodash/ (10MB)
│   └── ... (many more dependencies)
Total: ~250MB+ per function ❌
```

**Issues:**
- Each function exceeded AWS 250MB unzipped limit
- 20+ functions × 250MB = 5GB+ total deployment size
- Slow deployments due to large packages
- Duplicate dependencies across functions

### After: Layer Approach
```
Layer Package = Dependencies Only
├── nodejs/node_modules/
│   ├── pg/ (15MB)
│   ├── sequelize/ (25MB)
│   ├── axios/ (5MB)
│   ├── lodash/ (10MB)
│   └── ... (all dependencies)
Total Layer: ~80MB ✅

Function Package = Just Your Code
├── src/handlers/auth.js
├── src/handlers/categories.js
├── src/handlers/... (all your code)
Total Function: ~5-10MB ✅
```

## How Layers Work

### 1. Layer Structure
```
layer/
└── nodejs/                     # MUST be named 'nodejs' for Node.js runtime
    ├── package.json           # Dependencies definition
    └── node_modules/          # Installed dependencies (after npm install)
        ├── pg/
        ├── sequelize/
        ├── axios/
        └── ...
```

### 2. Runtime Behavior
When your Lambda function runs:

1. **AWS mounts the layer** at `/opt/nodejs/node_modules/`
2. **Node.js automatically finds** dependencies in this path
3. **Your code works normally** - `require('pg')` still works!

### 3. File System Layout in Lambda
```
/var/task/                      # Your function code
├── src/handlers/auth.js
└── src/handlers/categories.js

/opt/nodejs/node_modules/       # Layer dependencies (mounted by AWS)
├── pg/
├── sequelize/
└── axios/
```

## Implementation Details

### Layer Configuration (serverless.yml)
```yaml
layers:
  dependencies:
    path: layer                    # Points to layer/ directory
    name: ${self:service}-dependencies-${self:provider.stage}
    description: Heavy dependencies layer
    compatibleRuntimes:
      - nodejs22.x               # Only works with Node.js 22
    retain: false               # Delete old versions when deploying new ones
```

### Attach Layer to Functions
```yaml
provider:
  layers:
    - { Ref: DependenciesLambdaLayer }  # All functions get this layer
```

### Package Exclusion
```yaml
package:
  individually: false           # Single package for all functions
  patterns:
    - "!node_modules/**"        # Exclude ALL node_modules from function package
    - "!**/*.test.js"          # Exclude test files
    - "!tests/**"              # Exclude test directories
    - "src/**"                 # Include only your source code
```

### Layer Dependencies (layer/nodejs/package.json)
```json
{
  "name": "dependencies-layer",
  "version": "1.0.0",
  "description": "Dependencies layer for Lambda functions",
  "dependencies": {
    "pg": "^8.16.0",
    "sequelize": "^6.37.7",
    "pg-hstore": "^2.3.4",
    "axios": "^1.9.0",
    "jsonwebtoken": "^9.0.2",
    "joi": "^17.13.3",
    "lodash": "^4.17.21",
    "uuid": "^9.0.1",
    "qs": "^6.14.0",
    "express": "^5.1.0",
    "busboy": "^1.6.0",
    "dotenv": "^16.5.0"
  }
}
```

## Configuration Examples

### Complete serverless.yml Setup
```yaml
service: paris2-api-risk-assessment
frameworkVersion: '4'

# Layer definition
layers:
  dependencies:
    path: layer
    name: ${self:service}-dependencies-${self:provider.stage}
    description: Heavy dependencies layer
    compatibleRuntimes:
      - nodejs22.x
    retain: false

provider:
  name: aws
  runtime: nodejs22.x
  # Attach layer to all functions
  layers:
    - { Ref: DependenciesLambdaLayer }

# Exclude dependencies from function packages
package:
  individually: false
  patterns:
    - "!node_modules/**"
    - "src/**"

functions:
  auth:
    handler: src/handlers/auth.authenticate
  categories:
    handler: src/handlers/category.main
```

### Deployment Script (deploy.sh)
```bash
#!/bin/bash

# Build dependencies layer
echo "Building dependencies layer..."
cd layer/nodejs && npm install --production --no-optional && cd ../..

# Deploy with layer
echo "Deploying serverless application..."
NODE_OPTIONS="--max-old-space-size=4096" npx serverless deploy -s $ENV --region ${REGION}
```

## Benefits & Performance

### Size Comparison
| Approach | Function Size | Total Size | AWS Limit |
|----------|---------------|------------|-----------|
| **Before (Individual)** | 250MB+ each | 5GB+ | ❌ Exceeds 250MB |
| **After (Layers)** | 5-10MB each | 280MB total | ✅ Under limits |

### Key Benefits
1. **✅ Smaller Packages** - Functions only contain your code
2. **✅ Faster Deployments** - Less data to upload per function  
3. **✅ Shared Dependencies** - One layer serves all functions
4. **✅ Better Cold Starts** - Smaller function packages load faster
5. **✅ Version Management** - Update dependencies once in the layer
6. **✅ Cost Effective** - Reduced storage and transfer costs

### Performance Impact
- **Cold Start**: Improved due to smaller function packages
- **Deployment Time**: Reduced by ~70% (single layer vs multiple large packages)
- **Storage Cost**: Reduced by ~80% (shared dependencies)

## Best Practices

### 1. Layer Organization
- **Single Layer**: For related dependencies (database, utilities)
- **Multiple Layers**: For different concerns (database layer, auth layer, etc.)
- **Size Limit**: Keep layers under 250MB unzipped

### 2. Dependency Management
```bash
# Install only production dependencies in layer
cd layer/nodejs
npm install --production --no-optional
```

### 3. Version Control
- **Include layer/nodejs/package.json** in version control
- **Exclude layer/nodejs/node_modules/** from version control
- **Use package-lock.json** for consistent builds

### 4. Testing Locally
```bash
# Package and check sizes locally
npx serverless package -s dev
ls -lh .serverless/*.zip

# Verify layer contents
unzip -l .serverless/dependencies.zip | head -20
```

### 5. Monitoring
- **CloudWatch Logs**: Monitor layer loading times
- **Package Sizes**: Regular checks to ensure under limits
- **Cold Start Metrics**: Track performance improvements

## Deployment Process

1. **Build Layer**: `npm install` in `layer/nodejs/`
2. **Deploy Layer**: Serverless uploads layer to AWS
3. **Deploy Functions**: Serverless uploads tiny function packages  
4. **Runtime**: AWS automatically mounts layer for each function

## Troubleshooting

### Common Issues
- **Layer not found**: Check layer name reference
- **Dependencies missing**: Ensure layer is attached to functions
- **Version conflicts**: Use specific layer versions in production

### Debugging
```bash
# Check layer contents
aws lambda get-layer-version --layer-name your-layer --version-number 1

# Test function with layer
aws lambda invoke --function-name your-function response.json
```

---

This layer approach solved our 250MB package size limit issue and improved deployment performance significantly! 🚀
