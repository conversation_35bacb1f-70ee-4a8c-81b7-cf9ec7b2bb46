#!/bin/bash
export ENV=${ENV:-dev}
export REGION=${REGION:-ap-southeast-1}
export ACCOUNT_ID=${ACCOUNT:-************}
export BITBUCKET_USER_NAME=${BITBUCKET_USER_NAME}
export BITBUCKET_APP_PASSWORD=${BITBUCKET_APP_PASSWORD}
export PROFILE=${PROFILE}
export MAKE_ENV_FILE=${MAKE_ENV_FILE}

# deploy lambdas

echo_ssm_secret()
{
  KEY=$(jq -r ".credentials_params.$1" ./paris2-configuration.json)
  echo $(node load-env-var.js "${KEY}" $REGION $PROFILE)
}


export RISK_ASSESSMENT_DB_HOST=$(echo_ssm_secret "paris2_vessel_db_host")
export RISK_ASSESSMENT_DB_USER=$(echo_ssm_secret "paris2_risk_assessment_db_user")
export RISK_ASSESSMENT_DB_PASSWORD=$(echo_ssm_secret "paris2_risk_assessment_db_password")


export SONAR=$(echo_ssm_secret "sonar_auth_token")


if [ "$PARIS2_RISK_ASSESMENT_DB_CONNECTION" = 'null' ];
then
  echo 'missing PARIS2_RISK_ASSESMENT_DB_CONNECTION'
  exit 1
fi

if [ "$MAKE_ENV_FILE" = "true" ]; then
  echo "load env file with deploy/load-env-var.ts"
  npm run env:load
fi