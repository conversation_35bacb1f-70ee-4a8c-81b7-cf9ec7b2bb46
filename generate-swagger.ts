import swaggerJSDoc from "swagger-jsdoc";
import fs from "fs";
import path from "path";


const ENV = process.env.ENV;
console.log(`Environment: ${JSON.stringify(ENV, null, 2)}`);

const envFile = `${ENV}.json`;
const envConfigPath = path.resolve(__dirname, `./swagger/env/${envFile}`);
let serverUrl = "http://localhost:3000/stage";
let authorizationUrl = "http://localhost:3000/stage";

try {
  if (fs.existsSync(envConfigPath)) {
    const envConfig = JSON.parse(fs.readFileSync(envConfigPath, "utf-8"));
    serverUrl = envConfig.serverUrl || serverUrl;
    authorizationUrl = envConfig.authorizationUrl || authorizationUrl;
  } else {
    console.warn(`Environment configuration file not found: ${envConfigPath}`);
  }
} catch (error) {
  console.error(`Error reading environment configuration: ${error}`);
}

const swaggerConfig = {
  definition: {
    openapi: "3.0.3",
    info: {
      title: "Risk Assessment API",
      version: "1.0.0",
    },
    servers: [
      {
        url: serverUrl,
        variables: {
        }
      },
    ],
    paths: {
    },
    components: {
    schemas: {
    },
    securitySchemes: {
      keycloak: {
        type: "oauth2",
        description: "keycloak oauth",
        flows: {
          implicit: {
            authorizationUrl: authorizationUrl,
            scopes: {}
          }
        }
      }
    }
  },
  security: [
    {
      "keycloak": []
    }
  ]
  },
  apis: ["src/handlers/**/*.ts"],
};

const swaggerSpec = swaggerJSDoc(swaggerConfig as any);

const outputDir = path.resolve(__dirname, "./");
const outputPath = path.resolve(outputDir, `openapi.json`);

if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

fs.writeFileSync(outputPath, JSON.stringify(swaggerSpec, null, 2));
console.log(`✅ Swagger JSON generated at ${outputPath}`);
