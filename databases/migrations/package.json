{"name": "migrations", "version": "1.0.0", "description": "Project migrations", "main": "index.js", "scripts": {"db:migrate": "run-s db:migrate:*", "db:migrate-down": "run-s db:migrate-down:*", "db:migrate-reset": "run-s db:migrate-reset:*", "db:migrate:risk-assessment": "db-migrate up --config risk-assessment/database.json -m risk-assessment/migrations", "db:migrate-down:risk-assessment": "db-migrate down --config risk-assessment/database.json -m risk-assessment/migrations", "db:migrate-reset:risk-assessment": "db-migrate reset --config risk-assessment/database.json -m risk-assessment/migrations"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"db-migrate": "^0.11.14", "@aws-sdk/client-ssm": "^3.806.0", "db-migrate-pg": "^1.5.2", "npm-run-all": "^4.1.5", "pg": "^8.14.1"}}