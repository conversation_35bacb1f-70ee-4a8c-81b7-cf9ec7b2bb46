#!/bin/bash
stage=${ENV:-dev2}
export REGION=${REGION:-ap-southeast-1}
echo ${REGION}
releaseBranch=${releaseVersionId}
echo "releaseVersionId is ${releaseVersionId}"

echo_ssm_secret()
{
  echo $(aws --region ${REGION} ssm get-parameters --with-decryption --name $1 | jq '.Parameters[0].Value' | sed 's/"//g')
}
echo "Scanning code and uploading reports to SONAR SERVER..."

npx sonar-scanner \
  -Dsonar.host.url=https://sonar-dev.fleetship.com/\
  ${releaseVersionId:+-Dsonar.projectName="paris2-api-risk-assesment-R${releaseVersionId}"} \
  -Dsonar.login=$(echo_ssm_secret "/paris2-sonar-auth-token/${stage}") -X