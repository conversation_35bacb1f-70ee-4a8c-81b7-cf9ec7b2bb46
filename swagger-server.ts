import express from 'express';
import swaggerUi from 'swagger-ui-express';
import fs from 'fs';
import path from 'path';

const ENV = process.env.ENV || 'dev';
const swaggerPath = path.resolve(__dirname, `./openapi.json`);

// Safely read and parse the Swagger JSON file
let swaggerDocument: any;
try {
  const swaggerFileContent = fs.readFileSync(swaggerPath, 'utf8');
  swaggerDocument = JSON.parse(swaggerFileContent);
  
  // Dynamically add bearerAuth and global security requirement
  swaggerDocument.components = swaggerDocument.components || {};
  swaggerDocument.components.securitySchemes = {
    ...swaggerDocument.components.securitySchemes,
    bearerAuth: {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
    },
  };
  swaggerDocument.security = [
    ...(swaggerDocument.security || []),
    {
      bearerAuth: [],
    },
  ];

  // Add bearerAuth globally
  swaggerDocument.components = swaggerDocument.components || {};
  swaggerDocument.components.securitySchemes = {
    bearerAuth: {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
    },
  };
  swaggerDocument.security = [
    {
      bearerAuth: [],
    },
  ];
} catch (error) {
  console.error(`Failed to load Swagger JSON at ${swaggerPath}:`, error);
  process.exit(1);
}

const app = express();
app.use('/explorer', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

const PORT = parseInt("4000", 10);
app.listen(PORT, () => {
  console.log(`Swagger UI available at http://localhost:${PORT}/explorer`);
});
